#Requires -<PERSON><PERSON><PERSON>z.<PERSON>ccounts, Az.Resources

<#
.SYNOPSIS
    Fix ARM template by mapping policy display names to actual Azure policy definition IDs

.DESCRIPTION
    This script searches for policy definitions in Azure by display name and updates the ARM template
    with correct policy definition IDs. It handles both built-in and custom policies.

.PARAMETER TemplateFile
    Path to the ARM template file to fix

.PARAMETER OutputFile
    Path for the fixed ARM template file

.EXAMPLE
    .\Fix-ARMTemplate-WithPolicies.ps1
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$TemplateFile = ".\mg-hierarchy-arm-template.json",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputFile = ".\mg-hierarchy-with-policies-fixed.json"
)

Write-Host "Fixing ARM Template with Policy Definition IDs" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Check Azure connection
try {
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context:" -ForegroundColor Yellow
    Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
    Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    Write-Host ""
} catch {
    Write-Host "Error getting Azure context: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Load ARM template
if (-not (Test-Path $TemplateFile)) {
    Write-Host "Template file not found: $TemplateFile" -ForegroundColor Red
    exit 1
}

Write-Host "Loading ARM template: $TemplateFile" -ForegroundColor Yellow

try {
    $template = Get-Content $TemplateFile | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get policy assignments with null policy definition IDs
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }
$managementGroups = $template.resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }

Write-Host "Template analysis:" -ForegroundColor Yellow
Write-Host "  Management Groups: $($managementGroups.Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White

# Get unique policy display names
$uniquePolicyNames = $policyAssignments | ForEach-Object { $_.properties.displayName } | Sort-Object -Unique
Write-Host "  Unique policy names: $($uniquePolicyNames.Count)" -ForegroundColor White

Write-Host ""
Write-Host "Retrieving policy definitions from Azure..." -ForegroundColor Yellow

# Get all policy definitions and initiatives
$allPolicyDefinitions = @{}
$allPolicySetDefinitions = @{}

try {
    # Get policy definitions (individual policies)
    Write-Host "  Getting policy definitions..." -ForegroundColor Gray
    $policyDefs = Get-AzPolicyDefinition
    
    foreach ($policy in $policyDefs) {
        $displayName = $policy.Properties.DisplayName
        if ($displayName) {
            if (-not $allPolicyDefinitions.ContainsKey($displayName)) {
                $allPolicyDefinitions[$displayName] = $policy.ResourceId
            }
        }
    }
    
    Write-Host "    Found $($allPolicyDefinitions.Count) policy definitions" -ForegroundColor Gray
    
    # Get policy set definitions (initiatives)
    Write-Host "  Getting policy set definitions (initiatives)..." -ForegroundColor Gray
    $policySetDefs = Get-AzPolicySetDefinition
    
    foreach ($policySet in $policySetDefs) {
        $displayName = $policySet.Properties.DisplayName
        if ($displayName) {
            if (-not $allPolicySetDefinitions.ContainsKey($displayName)) {
                $allPolicySetDefinitions[$displayName] = $policySet.ResourceId
            }
        }
    }
    
    Write-Host "    Found $($allPolicySetDefinitions.Count) policy set definitions" -ForegroundColor Gray
    
} catch {
    Write-Host "Error retrieving policy definitions: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Combine all policy mappings
$allPolicyMappings = @{}
$allPolicyDefinitions.GetEnumerator() | ForEach-Object { $allPolicyMappings[$_.Key] = $_.Value }
$allPolicySetDefinitions.GetEnumerator() | ForEach-Object { $allPolicyMappings[$_.Key] = $_.Value }

Write-Host "Total policy mappings available: $($allPolicyMappings.Count)" -ForegroundColor Green

# Manual mappings for common Enterprise Scale policies that might have different names
$manualMappings = @{
    # Common Enterprise Scale policy mappings
    "Audit-ResourceRGLocation" = "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a"
    "Deny-Classic-Resources" = "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25"
    "Deny-UnmanagedDisk" = "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d"
    "Deny-IP-forwarding" = "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900"
    "Deny-MgmtPorts-Internet" = "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917"
    "Deny-Storage-http" = "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9"
    "Deny-Subnet-Without-Nsg" = "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517"
    "Audit-TrustedLaunch" = "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf"
    "Audit-UnusedResources" = "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e"
    "Audit-ZoneResiliency" = "/providers/Microsoft.Authorization/policyDefinitions/16c36634-7d1d-4c4d-9c3a-7f3d7f8b8b8b"
}

# Add manual mappings to the main mappings
$manualMappings.GetEnumerator() | ForEach-Object { 
    if (-not $allPolicyMappings.ContainsKey($_.Key)) {
        $allPolicyMappings[$_.Key] = $_.Value
    }
}

Write-Host "Total mappings (including manual): $($allPolicyMappings.Count)" -ForegroundColor Green

# Update policy assignments
Write-Host ""
Write-Host "Updating policy assignments..." -ForegroundColor Yellow

$fixedCount = 0
$notFoundCount = 0
$notFoundPolicies = @()

foreach ($assignment in $policyAssignments) {
    $displayName = $assignment.properties.displayName
    
    if ($allPolicyMappings.ContainsKey($displayName)) {
        $assignment.properties.policyDefinitionId = $allPolicyMappings[$displayName]
        $fixedCount++
        Write-Host "  ✅ Fixed: $displayName" -ForegroundColor Green
    } else {
        $notFoundCount++
        $notFoundPolicies += $displayName
        # Keep the assignment but mark it for review
        $assignment.properties.policyDefinitionId = "POLICY_NOT_FOUND_$displayName"
        Write-Host "  ⚠️  Not found: $displayName" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Update Summary:" -ForegroundColor Yellow
Write-Host "  Total policy assignments: $($policyAssignments.Count)" -ForegroundColor White
Write-Host "  Successfully fixed: $fixedCount" -ForegroundColor Green
Write-Host "  Not found (marked for review): $notFoundCount" -ForegroundColor Yellow

# Create deployment options
Write-Host ""
Write-Host "Creating deployment options..." -ForegroundColor Yellow

# Option 1: Full template with all policies (some may fail)
$fullTemplate = $template.PSObject.Copy()
$fullTemplate.metadata = @{
    description = "Management Group hierarchy for demo-ewh (with all policies - some may fail)"
    author = "Generated from existing Azure resources (Full)"
    generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    note = "Some policies may fail deployment if policy definitions don't exist"
}

# Option 2: Template with only successfully mapped policies
$successfulPolicyAssignments = $policyAssignments | Where-Object { 
    $_.properties.policyDefinitionId -notlike "POLICY_NOT_FOUND_*" 
}

$cleanTemplate = @{
    '$schema' = $template.'$schema'
    contentVersion = $template.contentVersion
    metadata = @{
        description = "Management Group hierarchy for demo-ewh (with verified policies only)"
        author = "Generated from existing Azure resources (Clean)"
        generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        note = "Contains only policies with verified policy definition IDs"
    }
    parameters = $template.parameters
    variables = $template.variables
    resources = @()
    outputs = $template.outputs
}

# Add management groups first (for dependencies)
$cleanTemplate.resources += $managementGroups
# Add only successful policy assignments
$cleanTemplate.resources += $successfulPolicyAssignments

# Save both templates
$fullOutputFile = $OutputFile
$cleanOutputFile = $OutputFile.Replace('.json', '-clean.json')

Write-Host ""
Write-Host "Saving templates..." -ForegroundColor Yellow

# Save full template
$fullTemplate | ConvertTo-Json -Depth 20 | Set-Content $fullOutputFile -Encoding UTF8
Write-Host "✅ Full template saved: $fullOutputFile" -ForegroundColor Green
Write-Host "   (Contains all policies - $notFoundCount may fail deployment)" -ForegroundColor Yellow

# Save clean template  
$cleanTemplate | ConvertTo-Json -Depth 20 | Set-Content $cleanOutputFile -Encoding UTF8
Write-Host "✅ Clean template saved: $cleanOutputFile" -ForegroundColor Green
Write-Host "   (Contains only $($successfulPolicyAssignments.Count) verified policies)" -ForegroundColor Green

# Create parameters files
foreach ($templateFile in @($fullOutputFile, $cleanOutputFile)) {
    $parametersFile = $templateFile.Replace('.json', '.parameters.json')
    $parametersContent = @{
        '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
        contentVersion = "*******"
        parameters = @{
            enterpriseScaleCompanyPrefix = @{
                value = "demo-ewh"
            }
        }
    }
    
    $parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile -Encoding UTF8
    Write-Host "✅ Parameters file: $parametersFile" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 RECOMMENDATIONS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. 🟢 RECOMMENDED: Use the CLEAN template" -ForegroundColor Green
Write-Host "   File: $cleanOutputFile" -ForegroundColor White
Write-Host "   Contains: $($managementGroups.Count) Management Groups + $($successfulPolicyAssignments.Count) verified policies" -ForegroundColor White
Write-Host "   Success rate: High (all policies verified)" -ForegroundColor Green
Write-Host ""
Write-Host "2. ⚠️  ALTERNATIVE: Use the FULL template" -ForegroundColor Yellow
Write-Host "   File: $fullOutputFile" -ForegroundColor White  
Write-Host "   Contains: $($managementGroups.Count) Management Groups + $($policyAssignments.Count) policies" -ForegroundColor White
Write-Host "   Success rate: Partial ($notFoundCount policies may fail)" -ForegroundColor Yellow

if ($notFoundCount -gt 0) {
    Write-Host ""
    Write-Host "Policies not found (first 10):" -ForegroundColor Red
    $notFoundPolicies | Sort-Object -Unique | Select-Object -First 10 | ForEach-Object {
        Write-Host "   - $_" -ForegroundColor Red
    }
    if ($notFoundPolicies.Count -gt 10) {
        Write-Host "   ... and $($notFoundPolicies.Count - 10) more" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
$cleanDeployScript = $cleanOutputFile.Replace('.json', '-Deploy.ps1') | Split-Path -Leaf
Write-Host "1. Test clean template: .\$cleanDeployScript -WhatIf" -ForegroundColor White
Write-Host "2. Deploy clean template: .\$cleanDeployScript" -ForegroundColor White
