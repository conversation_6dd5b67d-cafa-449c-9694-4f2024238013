#Requires -Mo<PERSON><PERSON> <PERSON><PERSON>.<PERSON>ccounts, Az.Resources

<#
.SYNOPSIS
    Fix ARM template by mapping policy display names to actual Azure policy definition IDs

.DESCRIPTION
    This script searches for policy definitions in Azure and updates the ARM template
    with correct policy definition IDs.

.EXAMPLE
    .\Fix-ARMTemplate-Simple.ps1
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$TemplateFile = ".\mg-hierarchy-arm-template.json",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputFile = ".\mg-hierarchy-with-policies-fixed.json"
)

Write-Host "Fixing ARM Template with Policy Definition IDs" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Check Azure connection
try {
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context:" -ForegroundColor Yellow
    Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
    Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    Write-Host ""
} catch {
    Write-Host "Error getting Azure context: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Load ARM template
if (-not (Test-Path $TemplateFile)) {
    Write-Host "Template file not found: $TemplateFile" -ForegroundColor Red
    exit 1
}

Write-Host "Loading ARM template: $TemplateFile" -ForegroundColor Yellow

try {
    $template = Get-Content $TemplateFile | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get policy assignments and management groups
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }
$managementGroups = $template.resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }

Write-Host "Template analysis:" -ForegroundColor Yellow
Write-Host "  Management Groups: $($managementGroups.Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White

# Get unique policy display names
$uniquePolicyNames = $policyAssignments | ForEach-Object { $_.properties.displayName } | Sort-Object -Unique
Write-Host "  Unique policy names: $($uniquePolicyNames.Count)" -ForegroundColor White

Write-Host ""
Write-Host "Retrieving policy definitions from Azure..." -ForegroundColor Yellow

# Get all policy definitions and initiatives
$allPolicyMappings = @{}

try {
    # Get policy definitions
    Write-Host "  Getting policy definitions..." -ForegroundColor Gray
    $policyDefs = Get-AzPolicyDefinition
    
    foreach ($policy in $policyDefs) {
        $displayName = $policy.Properties.DisplayName
        if ($displayName -and -not $allPolicyMappings.ContainsKey($displayName)) {
            $allPolicyMappings[$displayName] = $policy.ResourceId
        }
    }
    
    Write-Host "    Found $($policyDefs.Count) policy definitions" -ForegroundColor Gray
    
    # Get policy set definitions (initiatives)
    Write-Host "  Getting policy set definitions..." -ForegroundColor Gray
    $policySetDefs = Get-AzPolicySetDefinition
    
    foreach ($policySet in $policySetDefs) {
        $displayName = $policySet.Properties.DisplayName
        if ($displayName -and -not $allPolicyMappings.ContainsKey($displayName)) {
            $allPolicyMappings[$displayName] = $policySet.ResourceId
        }
    }
    
    Write-Host "    Found $($policySetDefs.Count) policy set definitions" -ForegroundColor Gray
    
} catch {
    Write-Host "Error retrieving policy definitions: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Total policy mappings available: $($allPolicyMappings.Count)" -ForegroundColor Green

# Update policy assignments
Write-Host ""
Write-Host "Updating policy assignments..." -ForegroundColor Yellow

$fixedCount = 0
$notFoundCount = 0
$successfulPolicyAssignments = @()

foreach ($assignment in $policyAssignments) {
    $displayName = $assignment.properties.displayName
    
    if ($allPolicyMappings.ContainsKey($displayName)) {
        $assignment.properties.policyDefinitionId = $allPolicyMappings[$displayName]
        $successfulPolicyAssignments += $assignment
        $fixedCount++
        Write-Host "  Fixed: $displayName" -ForegroundColor Green
    } else {
        $notFoundCount++
        Write-Host "  Not found: $displayName" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Update Summary:" -ForegroundColor Yellow
Write-Host "  Total policy assignments: $($policyAssignments.Count)" -ForegroundColor White
Write-Host "  Successfully fixed: $fixedCount" -ForegroundColor Green
Write-Host "  Not found: $notFoundCount" -ForegroundColor Yellow

# Create clean template with only successful policies
$cleanTemplate = @{
    '$schema' = $template.'$schema'
    contentVersion = $template.contentVersion
    metadata = @{
        description = "Management Group hierarchy for demo-ewh (with verified policies)"
        author = "Generated from existing Azure resources"
        generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    parameters = $template.parameters
    variables = $template.variables
    resources = @()
    outputs = $template.outputs
}

# Add management groups first
$cleanTemplate.resources += $managementGroups
# Add successful policy assignments
$cleanTemplate.resources += $successfulPolicyAssignments

# Save template
Write-Host ""
Write-Host "Saving template..." -ForegroundColor Yellow

try {
    $cleanTemplate | ConvertTo-Json -Depth 20 | Set-Content $OutputFile -Encoding UTF8
    Write-Host "Template saved: $OutputFile" -ForegroundColor Green
    Write-Host "Contains: $($managementGroups.Count) Management Groups + $($successfulPolicyAssignments.Count) policies" -ForegroundColor Green
} catch {
    Write-Host "Error saving template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create parameters file
$parametersFile = $OutputFile.Replace('.json', '.parameters.json')
$parametersContent = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = "demo-ewh"
        }
    }
}

$parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile -Encoding UTF8
Write-Host "Parameters file saved: $parametersFile" -ForegroundColor Green

# Create deployment script
$deploymentScript = $OutputFile.Replace('.json', '-Deploy.ps1')
$scriptContent = @"
#Requires -Modules Az.Accounts, Az.Resources

param(
    [Parameter(Mandatory = `$false)]
    [switch]`$WhatIf
)

`$templateFile = "$($OutputFile | Split-Path -Leaf)"
`$parametersFile = "$($parametersFile | Split-Path -Leaf)"
`$deploymentName = "mg-hierarchy-with-policies-`$(Get-Date -Format 'yyyyMMdd-HHmmss')"
`$location = "East US"

Write-Host "Deploying Management Group Hierarchy with Policies" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

if (`$WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    `$confirmation = Read-Host "Do you want to continue? (y/N)"
    if (`$confirmation -eq 'y' -or `$confirmation -eq 'Y') {
        New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile
    } else {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
    }
}
"@

$scriptContent | Set-Content $deploymentScript -Encoding UTF8
Write-Host "Deployment script saved: $deploymentScript" -ForegroundColor Green

Write-Host ""
Write-Host "Process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
$deployScriptName = $deploymentScript | Split-Path -Leaf
Write-Host "1. Test deployment: .\$deployScriptName -WhatIf" -ForegroundColor White
Write-Host "2. Deploy: .\$deployScriptName" -ForegroundColor White

if ($fixedCount -gt 0) {
    Write-Host ""
    Write-Host "SUCCESS: Template contains $fixedCount working policies!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "WARNING: No policies could be mapped. Consider using Management Groups only template." -ForegroundColor Yellow
}
