#Requires -Modules Az.Accounts, Az.Resources

<#
.SYNOPSIS
    Deploy Management Group hierarchy only (without policy assignments)

.DESCRIPTION
    This script deploys the Management Group hierarchy structure for demo-ewh
    without policy assignments to avoid deployment issues with null policy definition IDs.

.PARAMETER WhatIf
    Run in What-If mode to preview changes without deploying

.PARAMETER CompanyPrefix
    Company prefix for Management Group names (default: demo-ewh)

.EXAMPLE
    .\Deploy-ManagementGroups-Only.ps1 -WhatIf
    Preview the deployment

.EXAMPLE
    .\Deploy-ManagementGroups-Only.ps1
    Deploy the Management Groups
#>

param(
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory = $false)]
    [string]$CompanyPrefix = "demo-ewh"
)

# Script variables
$templateFile = ".\mg-hierarchy-only.json"
$parametersFile = ".\mg-hierarchy-only.parameters.json"
$deploymentName = "mg-hierarchy-only-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$location = "East US"

Write-Host "Deploying Management Group Hierarchy (Management Groups Only)" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Check if user is logged in to Azure
try {
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context:" -ForegroundColor Yellow
    Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
    Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    Write-Host ""
} catch {
    Write-Host "Error getting Azure context: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Validate template files exist
if (-not (Test-Path $templateFile)) {
    Write-Host "Template file not found: $templateFile" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $parametersFile)) {
    Write-Host "Parameters file not found: $parametersFile" -ForegroundColor Red
    exit 1
}

Write-Host "Deployment Configuration:" -ForegroundColor Yellow
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor White
Write-Host "  Location: $location" -ForegroundColor White
Write-Host "  Template File: $templateFile" -ForegroundColor White
Write-Host "  Parameters File: $parametersFile" -ForegroundColor White
Write-Host "  Company Prefix: $CompanyPrefix" -ForegroundColor White
Write-Host "  What-If Mode: $WhatIf" -ForegroundColor White
Write-Host ""

# Update parameters with company prefix
$parametersContent = Get-Content $parametersFile | ConvertFrom-Json
$parametersContent.parameters.enterpriseScaleCompanyPrefix.value = $CompanyPrefix
$parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile

try {
    if ($WhatIf) {
        Write-Host "Running What-If deployment..." -ForegroundColor Yellow
        $result = New-AzTenantDeployment `
            -Name $deploymentName `
            -Location $location `
            -TemplateFile $templateFile `
            -TemplateParameterFile $parametersFile `
            -WhatIf
        
        Write-Host "What-If deployment completed successfully!" -ForegroundColor Green
        Write-Host "Review the changes above before running the actual deployment." -ForegroundColor Yellow
    } else {
        Write-Host "Starting deployment..." -ForegroundColor Yellow
        Write-Host "WARNING: This will create Management Groups!" -ForegroundColor Red
        
        $confirmation = Read-Host "Do you want to continue? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Host "Deployment cancelled by user." -ForegroundColor Yellow
            exit 0
        }
        
        $result = New-AzTenantDeployment `
            -Name $deploymentName `
            -Location $location `
            -TemplateFile $templateFile `
            -TemplateParameterFile $parametersFile
        
        if ($result.ProvisioningState -eq "Succeeded") {
            Write-Host "Deployment completed successfully!" -ForegroundColor Green
            Write-Host "Management Groups created:" -ForegroundColor Yellow
            $result.Outputs.managementGroupIds.Value | ForEach-Object {
                Write-Host "  - $_" -ForegroundColor White
            }
        } else {
            Write-Host "Deployment failed with state: $($result.ProvisioningState)" -ForegroundColor Red
            Write-Host "Error details: $($result.Error)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Deployment failed with error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
