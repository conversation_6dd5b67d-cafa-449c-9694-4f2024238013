#Requires -Mo<PERSON>les <PERSON>z.<PERSON>ccounts, Az.Resources

<#
.SYNOPSIS
    Get existing policy assignments from Azure Management Groups to extract policy definition IDs

.DESCRIPTION
    This script retrieves existing policy assignments from the demo-ewh management group hierarchy
    to extract the actual policy definition IDs that are currently in use.

.EXAMPLE
    .\Get-ExistingPolicyAssignments.ps1
#>

param()

Write-Host "Retrieving Existing Policy Assignments from Azure" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if user is logged in to Azure
try {
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context:" -ForegroundColor Yellow
    Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
    Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    Write-Host ""
} catch {
    Write-Host "Error getting Azure context: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Management groups to check
$managementGroups = @(
    "demo-ewh",
    "demo-ewh-landingzones",
    "demo-ewh-online", 
    "demo-ewh-corp",
    "demo-ewh-decommissioned",
    "demo-ewh-sandboxes",
    "demo-ewh-platform",
    "demo-ewh-connectivity",
    "demo-ewh-management",
    "demo-ewh-identity",
    "demo-ewh-security"
)

$allPolicyAssignments = @()
$policyMappings = @{}

Write-Host "Retrieving policy assignments from management groups..." -ForegroundColor Yellow

foreach ($mgName in $managementGroups) {
    Write-Host "  Checking management group: $mgName" -ForegroundColor Gray
    
    try {
        # Get policy assignments for this management group
        $assignments = Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/$mgName" -ErrorAction SilentlyContinue
        
        if ($assignments) {
            Write-Host "    Found $($assignments.Count) policy assignments" -ForegroundColor Green
            
            foreach ($assignment in $assignments) {
                $allPolicyAssignments += $assignment
                
                # Create mapping from display name to policy definition ID
                $displayName = $assignment.Properties.DisplayName
                $policyDefId = $assignment.Properties.PolicyDefinitionId
                
                if ($displayName -and $policyDefId -and -not $policyMappings.ContainsKey($displayName)) {
                    $policyMappings[$displayName] = $policyDefId
                    Write-Host "      ✅ $displayName -> $policyDefId" -ForegroundColor Green
                }
            }
        } else {
            Write-Host "    No policy assignments found" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "    ❌ Error accessing management group: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "  Total policy assignments found: $($allPolicyAssignments.Count)" -ForegroundColor White
Write-Host "  Unique policy mappings: $($policyMappings.Count)" -ForegroundColor White

# Save policy mappings to JSON file
$mappingsFile = ".\policy-mappings.json"
$policyMappings | ConvertTo-Json -Depth 10 | Set-Content $mappingsFile -Encoding UTF8
Write-Host "  Policy mappings saved to: $mappingsFile" -ForegroundColor Green

# Create detailed report
$reportFile = ".\existing-policy-assignments-report.json"
$report = @{
    GeneratedDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    TenantId = $context.Tenant.Id
    Subscription = $context.Subscription.Name
    ManagementGroups = $managementGroups
    TotalAssignments = $allPolicyAssignments.Count
    UniquePolicyMappings = $policyMappings.Count
    PolicyMappings = $policyMappings
    DetailedAssignments = @()
}

foreach ($assignment in $allPolicyAssignments) {
    $report.DetailedAssignments += @{
        Name = $assignment.Name
        DisplayName = $assignment.Properties.DisplayName
        PolicyDefinitionId = $assignment.Properties.PolicyDefinitionId
        Scope = $assignment.Properties.Scope
        EnforcementMode = $assignment.Properties.EnforcementMode
        ResourceId = $assignment.ResourceId
    }
}

$report | ConvertTo-Json -Depth 10 | Set-Content $reportFile -Encoding UTF8
Write-Host "  Detailed report saved to: $reportFile" -ForegroundColor Green

# Show sample of policy mappings
if ($policyMappings.Count -gt 0) {
    Write-Host ""
    Write-Host "Sample Policy Mappings (first 10):" -ForegroundColor Yellow
    
    $counter = 0
    foreach ($mapping in $policyMappings.GetEnumerator()) {
        if ($counter -ge 10) { break }
        Write-Host "  $($mapping.Key) -> $($mapping.Value)" -ForegroundColor White
        $counter++
    }
    
    if ($policyMappings.Count -gt 10) {
        Write-Host "  ... and $($policyMappings.Count - 10) more" -ForegroundColor Gray
    }
} else {
    Write-Host ""
    Write-Host "❌ No policy assignments found!" -ForegroundColor Red
    Write-Host "This could mean:" -ForegroundColor Yellow
    Write-Host "  1. Management groups don't exist yet" -ForegroundColor White
    Write-Host "  2. No policies are assigned" -ForegroundColor White
    Write-Host "  3. Insufficient permissions to read policy assignments" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 Analysis completed!" -ForegroundColor Green

if ($policyMappings.Count -gt 0) {
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Use the policy mappings to update your ARM template" -ForegroundColor White
    Write-Host "2. Run the update script with the mappings file" -ForegroundColor White
}
