# Azure Policy Module

## 📋 Tổng quan

Module này cung cấp các công cụ để quản lý Azure Policy definitions, initiatives và policy assignments trong Azure Landing Zone. Module được tách riêng từ template chính để dễ dàng quản lý và sử dụng độc lập.

## 🏗️ Cấu trúc Module

```
azure-policies/
├── 📄 README.md                              # Tài liệu hướng dẫn
├── 📁 policy-definitions/                    # Policy definitions
│   ├── 📄 policies.json                      # Custom policy definitions
│   └── 📄 Deploy-PolicyDefinitions.ps1      # Script deploy policy definitions
├── 📁 policy-initiatives/                   # Policy initiatives (sets)
│   ├── 📄 initiatives.json                  # Policy initiatives
│   └── 📄 Deploy-PolicyInitiatives.ps1      # Script deploy initiatives
├── 📁 policy-assignments/                   # Policy assignments
│   ├── 📁 security/                         # Security policies
│   ├── 📁 compliance/                       # Compliance policies
│   ├── 📁 monitoring/                       # Monitoring policies
│   ├── 📁 networking/                       # Network policies
│   └── 📄 Deploy-PolicyAssignments.ps1      # Script deploy assignments
├── 📁 workload-specific/                    # Workload specific policies
│   ├── 📁 api-management/
│   ├── 📁 app-services/
│   ├── 📁 storage/
│   ├── 📁 sql/
│   └── 📁 kubernetes/
└── 📁 examples/                             # Ví dụ và templates
    ├── 📄 policy-assignment-example.json
    └── 📄 bulk-assignment-example.json
```

## 🚀 Cách sử dụng

### 1. Deploy Policy Definitions

```powershell
cd azure-policies/policy-definitions
.\Deploy-PolicyDefinitions.ps1 -EnterpriseScaleCompanyPrefix "ewh"
```

### 2. Deploy Policy Initiatives

```powershell
cd ../policy-initiatives
.\Deploy-PolicyInitiatives.ps1 -EnterpriseScaleCompanyPrefix "ewh"
```

### 3. Deploy Policy Assignments

```powershell
cd ../policy-assignments
.\Deploy-PolicyAssignments.ps1 `
    -EnterpriseScaleCompanyPrefix "ewh" `
    -PolicyType "Security" `
    -ManagementGroupId "ewh-Platform-Management"
```

## 📝 Các loại Policy được hỗ trợ

### 🔒 Security Policies
- **DENY**: Chặn các hành động không an toàn
- **AUDIT**: Kiểm tra compliance
- **DINE**: Deploy If Not Exists - Tự động deploy security configurations

### 🌐 Network Policies
- **Subnet NSG**: Yêu cầu NSG cho subnet
- **Public IP**: Kiểm soát public IP
- **Private Endpoints**: Enforce private connectivity

### 📊 Monitoring Policies
- **VM Monitoring**: Azure Monitor cho VMs
- **Log Analytics**: Centralized logging
- **Diagnostic Settings**: Resource diagnostics

### 🏢 Workload Specific Policies
- **API Management**: APIM guardrails
- **App Services**: Web app security
- **Storage**: Storage account security
- **SQL**: Database security
- **Kubernetes**: AKS security

## 🔧 Tính năng chính

### ✅ Policy Definitions
- Custom policy definitions cho Azure Landing Zone
- Built-in policy references
- Parameter validation
- Effect types: Deny, Audit, DeployIfNotExists, Modify

### ✅ Policy Initiatives (Sets)
- Grouped policies cho specific scenarios
- Security baseline initiatives
- Compliance frameworks
- Workload-specific initiatives

### ✅ Policy Assignments
- Management Group level assignments
- Subscription level assignments
- Resource Group level assignments
- Parameter customization
- Enforcement modes

## 📋 Policy Categories

### 1. **Platform Policies**
Áp dụng cho Platform Management Groups:
- Identity management
- Connectivity controls
- Management tools

### 2. **Landing Zone Policies**
Áp dụng cho Landing Zone Management Groups:
- Application security
- Data protection
- Network isolation

### 3. **Sandbox Policies**
Áp dụng cho Sandbox environments:
- Resource limitations
- Cost controls
- Temporary access

### 4. **Decommissioned Policies**
Áp dụng cho Decommissioned resources:
- Resource cleanup
- Access restrictions
- Data retention

## 🔗 Tích hợp với Management Groups Module

Module này được thiết kế để hoạt động cùng với Management Groups module:

```powershell
# 1. Tạo Management Group hierarchy
cd ../management-groups
.\Deploy-ManagementGroups.ps1 -EnterpriseScaleCompanyPrefix "ewh" -Location "East Asia"

# 2. Deploy Policy Definitions
cd ../azure-policies/policy-definitions
.\Deploy-PolicyDefinitions.ps1 -EnterpriseScaleCompanyPrefix "ewh"

# 3. Deploy Policy Initiatives
cd ../policy-initiatives
.\Deploy-PolicyInitiatives.ps1 -EnterpriseScaleCompanyPrefix "ewh"

# 4. Assign Policies to Management Groups
cd ../policy-assignments
.\Deploy-PolicyAssignments.ps1 -EnterpriseScaleCompanyPrefix "ewh"
```

## 🛠️ Troubleshooting

**1. "Policy definition not found"**
```powershell
# Kiểm tra policy definitions
Get-AzPolicyDefinition -ManagementGroupName "ewh"
```

**2. "Insufficient permissions"**
```powershell
# Kiểm tra quyền Policy Contributor
Get-AzRoleAssignment -Scope "/providers/Microsoft.Management/managementGroups/ewh"
```

**3. "Assignment conflict"**
```powershell
# Kiểm tra existing assignments
Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/ewh"
```

## 📚 Tài liệu tham khảo

- [Azure Policy Documentation](https://docs.microsoft.com/en-us/azure/governance/policy/)
- [Azure Landing Zone Policies](https://docs.microsoft.com/en-us/azure/cloud-adoption-framework/ready/enterprise-scale/architecture)
- [Policy Definition Structure](https://docs.microsoft.com/en-us/azure/governance/policy/concepts/definition-structure)
