# Simple test script for Azure Policy Definitions

param(
    [Parameter(Mandatory = $true)]
    [string]$EnterpriseScaleCompanyPrefix,
    
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

Write-Host "Testing Azure Policy Definitions deployment..." -ForegroundColor Green
Write-Host "Management Group: $EnterpriseScaleCompanyPrefix" -ForegroundColor White

# Check if template file exists
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$templateFile = Join-Path $scriptPath "policies.json"

if (Test-Path $templateFile) {
    Write-Host "✅ Template file found: $templateFile" -ForegroundColor Green
} else {
    Write-Host "❌ Template file not found: $templateFile" -ForegroundColor Red
    exit 1
}

# Check Azure context
try {
    $context = Get-AzContext
    if ($context) {
        Write-Host "✅ Azure context found" -ForegroundColor Green
        Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
        Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    } else {
        Write-Host "❌ No Azure context found. Please run Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Azure PowerShell modules not available" -ForegroundColor Red
    Write-Host "Please install: Install-Module Az.Accounts, Az.Resources" -ForegroundColor Yellow
    exit 1
}

if ($WhatIf) {
    Write-Host "`n🔍 What-If mode - would deploy policy definitions to management group: $EnterpriseScaleCompanyPrefix" -ForegroundColor Yellow
} else {
    Write-Host "`n🚀 Ready to deploy policy definitions to management group: $EnterpriseScaleCompanyPrefix" -ForegroundColor Cyan
}

Write-Host "`n✅ Test completed successfully!" -ForegroundColor Green
