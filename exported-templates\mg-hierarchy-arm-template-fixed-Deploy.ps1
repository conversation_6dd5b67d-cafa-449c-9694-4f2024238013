﻿#Requires -Mo<PERSON>les <PERSON><PERSON>.Accounts, Az.Resources

param(
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

$templateFile = "mg-hierarchy-arm-template-fixed.json"
$parametersFile = "mg-hierarchy-arm-template-fixed.parameters.json"
$deploymentName = "mg-hierarchy-fixed-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$location = "East US"

if ($WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile
}
