﻿{
    "outputs":  {
                    "managementGroupIds":  {
                                               "value":  [
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-landingzones\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-online\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-corp\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-decommissioned\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-sandboxes\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-platform\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-connectivity\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-management\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-identity\u0027)]",
                                                             "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-security\u0027)]"
                                                         ],
                                               "type":  "array"
                                           }
                },
    "metadata":  {
                     "generatedDate":  "2025-09-06 16:25:47",
                     "description":  "Management Group hierarchy for demo-ewh (Management Groups only)",
                     "successRate":  "100%",
                     "author":  "Generated from existing Azure resources",
                     "deploymentType":  "Management Groups Only"
                 },
    "resources":  [
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u00272c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0\u0027)]"
                                        ],
                          "name":  "demo-ewh",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-landingzones",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh\u0027)]"
                                        ],
                          "name":  "demo-ewh-landingzones",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-online",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-landingzones\u0027)]"
                                        ],
                          "name":  "demo-ewh-online",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-corp",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-landingzones\u0027)]"
                                        ],
                          "name":  "demo-ewh-corp",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-decommissioned",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh\u0027)]"
                                        ],
                          "name":  "demo-ewh-decommissioned",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-sandboxes",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh\u0027)]"
                                        ],
                          "name":  "demo-ewh-sandboxes",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-platform",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh\u0027)]"
                                        ],
                          "name":  "demo-ewh-platform",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-connectivity",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-platform\u0027)]"
                                        ],
                          "name":  "demo-ewh-connectivity",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-management",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-platform\u0027)]"
                                        ],
                          "name":  "demo-ewh-management",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-identity",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-platform\u0027)]"
                                        ],
                          "name":  "demo-ewh-identity",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      },
                      {
                          "properties":  {
                                             "displayName":  "demo-ewh-security",
                                             "details":  {
                                                             "parent":  {
                                                                            "id":  "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"
                                                                        }
                                                         }
                                         },
                          "dependsOn":  [
                                            "[resourceId(\u0027Microsoft.Management/managementGroups\u0027, \u0027demo-ewh-platform\u0027)]"
                                        ],
                          "name":  "demo-ewh-security",
                          "apiVersion":  "2021-04-01",
                          "type":  "Microsoft.Management/managementGroups"
                      }
                  ],
    "contentVersion":  "1.0.0.0",
    "variables":  {

                  },
    "parameters":  {
                       "enterpriseScaleCompanyPrefix":  {
                                                            "maxLength":  10,
                                                            "defaultValue":  "demo-ewh",
                                                            "type":  "string",
                                                            "metadata":  {
                                                                             "description":  "Company prefix for Management Group hierarchy"
                                                                         }
                                                        }
                   },
    "$schema":  "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#"
}
