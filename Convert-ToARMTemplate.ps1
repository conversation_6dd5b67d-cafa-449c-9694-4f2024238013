<#
.SYNOPSIS
    Convert Management Group Hierarchy and Policy Assignments to ARM Template

.DESCRIPTION
    This script converts the management group hierarchy and policy assignments
    from the Check-ManagementGroupHierarchy.ps1 output to ARM template format.

.PARAMETER InputJsonPath
    Path to the JSON file containing the hierarchy data

.PARAMETER OutputPath
    Path to save the generated ARM template

.PARAMETER TemplatePrefix
    Prefix for the ARM template resources

.EXAMPLE
    .\Convert-ToARMTemplate.ps1 -InputJsonPath "management-group-hierarchy-demo-ewh.json"

.EXAMPLE
    .\Convert-ToARMTemplate.ps1 -InputJsonPath "hierarchy.json" -OutputPath "C:\temp" -TemplatePrefix "ewh"
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$InputJsonPath,

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".",

    [Parameter(Mandatory = $false)]
    [string]$TemplatePrefix = "demo-ewh"
)

# Check if input file exists
if (-not (Test-Path $InputJsonPath)) {
    Write-Error "Input JSON file not found: $InputJsonPath"
    exit 1
}

# Read the hierarchy data
try {
    $hierarchyData = Get-Content -Path $InputJsonPath -Raw | ConvertFrom-Json
    Write-Host "✅ Successfully loaded hierarchy data from: $InputJsonPath" -ForegroundColor Green
}
catch {
    Write-Error "Failed to read or parse JSON file: $($_.Exception.Message)"
    exit 1
}

# Function to create management group ARM resources
function New-ManagementGroupResource {
    param(
        [object]$MgInfo,
        [array]$Resources = @()
    )
    
    # Create management group resource
    $mgResource = @{
        type = "Microsoft.Management/managementGroups"
        apiVersion = "2021-04-01"
        name = $MgInfo.Id
        properties = @{
            displayName = $MgInfo.DisplayName
        }
    }
    
    # Add parent reference if not root
    if ($MgInfo.ParentId) {
        $mgResource.properties.details = @{
            parent = @{
                id = $MgInfo.ParentId
            }
        }
        # Add dependency on parent
        $parentName = ($MgInfo.ParentId -split '/')[-1]
        $mgResource.dependsOn = @("[resourceId('Microsoft.Management/managementGroups', '$parentName')]")
    }
    
    $Resources += $mgResource
    
    # Create policy assignment resources for this management group
    if ($MgInfo.PolicyAssignments) {
        foreach ($policy in $MgInfo.PolicyAssignments) {
            $policyResource = @{
                type = "Microsoft.Authorization/policyAssignments"
                apiVersion = "2022-06-01"
                name = $policy.Name
                properties = @{
                    displayName = $policy.DisplayName
                    policyDefinitionId = $policy.PolicyDefinitionId
                    scope = "/providers/Microsoft.Management/managementGroups/$($MgInfo.Id)"
                    enforcementMode = if ($policy.EnforcementMode) { $policy.EnforcementMode } else { "Default" }
                }
                dependsOn = @("[resourceId('Microsoft.Management/managementGroups', '$($MgInfo.Id)')]")
            }
            
            # Add parameters if they exist
            if ($policy.Parameters) {
                $policyResource.properties.parameters = $policy.Parameters
            }
            
            # Add identity if it exists
            if ($policy.Identity) {
                $policyResource.identity = $policy.Identity
            }
            
            $Resources += $policyResource
        }
    }
    
    # Process child management groups
    foreach ($child in $MgInfo.Children) {
        $Resources = New-ManagementGroupResource -MgInfo $child -Resources $Resources
    }
    
    return $Resources
}

# Create ARM template structure
$armTemplate = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#"
    contentVersion = "*******"
    metadata = @{
        description = "ARM template for Management Group hierarchy and Policy assignments"
        author = "Generated from existing Azure resources"
        generatedDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        sourceData = @{
            tenantId = $hierarchyData.TenantId
            subscriptionId = $hierarchyData.SubscriptionId
            exportDate = $hierarchyData.ExportDate
        }
    }
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            type = "string"
            defaultValue = $TemplatePrefix
            maxLength = 10
            metadata = @{
                description = "Provide a prefix (max 10 characters) for the Management Group hierarchy"
            }
        }
    }
    variables = @{}
    resources = @()
}

Write-Host "🔄 Converting hierarchy to ARM template resources..." -ForegroundColor Cyan

# Generate resources from hierarchy
$resources = New-ManagementGroupResource -MgInfo $hierarchyData.ManagementGroupHierarchy

# Add resources to template
$armTemplate.resources = $resources

Write-Host "📊 ARM Template Summary:" -ForegroundColor Green
Write-Host "  Management Groups: $(($resources | Where-Object { $_.type -eq 'Microsoft.Management/managementGroups' }).Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $(($resources | Where-Object { $_.type -eq 'Microsoft.Authorization/policyAssignments' }).Count)" -ForegroundColor White

# Save ARM template
$templatePath = Join-Path $OutputPath "management-group-hierarchy-template.json"
$armTemplate | ConvertTo-Json -Depth 20 | Out-File -FilePath $templatePath -Encoding UTF8

Write-Host "`n💾 ARM template saved to: $templatePath" -ForegroundColor Green

# Create parameters file
$parametersFile = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = $TemplatePrefix
        }
    }
}

$parametersPath = Join-Path $OutputPath "management-group-hierarchy-template.parameters.json"
$parametersFile | ConvertTo-Json -Depth 10 | Out-File -FilePath $parametersPath -Encoding UTF8

Write-Host "💾 Parameters file saved to: $parametersPath" -ForegroundColor Green

Write-Host "`n🚀 Deployment Instructions:" -ForegroundColor Cyan
Write-Host "To deploy this template, run:" -ForegroundColor White
Write-Host "New-AzTenantDeployment -Location 'East US' -TemplateFile '$templatePath' -TemplateParameterFile '$parametersPath'" -ForegroundColor Yellow

Write-Host "`n✅ ARM template conversion completed!" -ForegroundColor Green
