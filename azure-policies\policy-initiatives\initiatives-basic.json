{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}}, "variables": {}, "resources": [{"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "ALZ-Security-Baseline", "properties": {"displayName": "Azure Landing Zone Security Baseline", "description": "This initiative includes security policies for Azure Landing Zone", "policyType": "Custom", "metadata": {"category": "Security", "source": "Azure Landing Zone"}, "policyDefinitions": [{"policyDefinitionId": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-Classic-Resources')]"}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9"}]}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "ALZ-Network-Security", "dependsOn": [], "properties": {"displayName": "Azure Landing Zone Network Security", "description": "This initiative includes network security policies for Azure Landing Zone", "policyType": "Custom", "metadata": {"category": "Network", "source": "Azure Landing Zone"}, "policyDefinitions": [{"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e372f825-a257-4fb8-9175-797a8a8627d6"}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/564feb30-bf6a-4854-b4bb-0d2d2d1e6c66"}]}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "ALZ-Data-Protection", "properties": {"displayName": "Azure Landing Zone Data Protection", "description": "This initiative includes data protection policies for Azure Landing Zone", "policyType": "Custom", "metadata": {"category": "Data Protection", "source": "Azure Landing Zone"}, "policyDefinitions": [{"policyDefinitionId": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policyDefinitions/Require-Storage-Https')]"}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917"}]}}], "outputs": {"policySetDefinitionIds": {"type": "object", "value": {"securityBaseline": "[resourceId('Microsoft.Authorization/policySetDefinitions', 'ALZ-Security-Baseline')]", "dataProtection": "[resourceId('Microsoft.Authorization/policySetDefinitions', 'ALZ-Data-Protection')]"}}}}