# Management Group Hierarchy Export to ARM Template

Bộ script này giúp bạn kiểm tra cấu trúc hierarchy của Management Group trên Azure và chuyển đổi thành ARM template để có thể tái tạo lại.

## 📋 Mô tả

Bộ script bao gồm:

1. **Check-ManagementGroupHierarchy.ps1** - Ki<PERSON>m tra cấu trúc hierarchy và policy assignments
2. **Convert-ToARMTemplate.ps1** - Chuyển đổi dữ liệu thành ARM template
3. **Export-ManagementGroupToARM.ps1** - Script tổng hợp chạy toàn bộ quy trình

## 🚀 Cách sử dụng

### Phương pháp 1: Sử dụng script tổng hợp (Khuyến nghị)

```powershell
# Chạy toàn bộ quy trình export
.\Export-ManagementGroupToARM.ps1 -ManagementGroupPrefix "demo-ewh"

# Hoặc chỉ định thư mục output
.\Export-ManagementGroupToARM.ps1 -ManagementGroupPrefix "demo-ewh" -OutputPath "C:\temp\export"
```

### Phương pháp 2: Chạy từng bước

#### Bước 1: Kiểm tra hierarchy và export JSON
```powershell
.\Check-ManagementGroupHierarchy.ps1 -ManagementGroupPrefix "demo-ewh" -ExportToJson
```

#### Bước 2: Chuyển đổi thành ARM template
```powershell
.\Convert-ToARMTemplate.ps1 -InputJsonPath "management-group-hierarchy-demo-ewh.json" -TemplatePrefix "demo-ewh"
```

## 📊 Kết quả

Script sẽ tạo ra các file sau:

### 1. JSON Export
- `management-group-hierarchy-demo-ewh.json` - Dữ liệu hierarchy và policy assignments

### 2. ARM Template
- `management-group-hierarchy-template.json` - ARM template chính
- `management-group-hierarchy-template.parameters.json` - File parameters

### 3. Thông tin được export

#### Management Group Hierarchy
- Tên và ID của từng Management Group
- Cấu trúc parent-child relationship
- Danh sách subscriptions trong mỗi Management Group

#### Policy Assignments
- Tên và ID của policy assignments
- Policy definition ID
- Scope và parameters
- Enforcement mode
- Identity information (nếu có)

## 🔧 Yêu cầu

### PowerShell Modules
- `Az.Accounts`
- `Az.Resources`
- `Az.Profile`

### Quyền truy cập Azure
- Reader permission trên Management Group cần kiểm tra
- Policy Reader permission để đọc policy assignments

## 📝 Ví dụ Output

### Console Output
```
🚀 Management Group to ARM Template Export Workflow
====================================================
Management Group Prefix: demo-ewh
Output Path: .\exported-templates

📊 Step 1: Checking Management Group Hierarchy...
📁 Demo EWH Root (demo-ewh)
  📁 Platform (demo-ewh-platform)
    📁 Management (demo-ewh-management)
    📁 Connectivity (demo-ewh-connectivity)
    📁 Identity (demo-ewh-identity)
  📁 Landing Zones (demo-ewh-landingzones)
    📁 Corp (demo-ewh-corp)
    📁 Online (demo-ewh-online)

🎯 Step 2: Retrieving Policy Assignments...
Management Group: Demo EWH Root (demo-ewh)
    🎯 Policy: Require encryption for storage accounts
    🎯 Policy: Deny public IP creation

💾 Step 3: Exporting to JSON...
✅ Hierarchy exported to: .\exported-templates\management-group-hierarchy-demo-ewh.json
```

### ARM Template Structure
```json
{
  "$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#",
  "contentVersion": "*******",
  "parameters": {
    "enterpriseScaleCompanyPrefix": {
      "type": "string",
      "defaultValue": "demo-ewh"
    }
  },
  "resources": [
    {
      "type": "Microsoft.Management/managementGroups",
      "apiVersion": "2021-04-01",
      "name": "demo-ewh",
      "properties": {
        "displayName": "Demo EWH Root"
      }
    }
  ]
}
```

## 🚀 Deploy ARM Template

Sau khi có ARM template, bạn có thể deploy bằng:

```powershell
# Deploy tại tenant level
New-AzTenantDeployment `
  -Location "East US" `
  -TemplateFile "management-group-hierarchy-template.json" `
  -TemplateParameterFile "management-group-hierarchy-template.parameters.json"
```

## ⚠️ Lưu ý

1. **Quyền truy cập**: Đảm bảo bạn có đủ quyền để đọc Management Groups và Policy Assignments
2. **Tenant deployment**: ARM template sẽ được deploy ở tenant level
3. **Existing resources**: Nếu Management Groups đã tồn tại, deployment có thể fail
4. **Policy definitions**: Script chỉ export policy assignments, không export policy definitions

## 🔍 Troubleshooting

### Lỗi "Management group not found"
- Kiểm tra tên Management Group prefix
- Đảm bảo bạn có quyền truy cập

### Lỗi "Access denied"
- Kiểm tra quyền Reader trên Management Group
- Kiểm tra quyền Policy Reader

### Lỗi khi deploy ARM template
- Kiểm tra xem Management Groups đã tồn tại chưa
- Sử dụng tham số `-WhatIf` để preview trước khi deploy

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Azure PowerShell modules đã được cài đặt
2. Đã đăng nhập vào Azure (`Connect-AzAccount`)
3. Có đủ quyền truy cập Management Groups
4. Management Group prefix chính xác
