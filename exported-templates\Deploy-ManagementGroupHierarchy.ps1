# Deploy Management Group Hierarchy ARM Template
param(
    [string]$Location = "East US",
    [string]$TemplateFile = ".\mg-hierarchy-arm-template.json",
    [string]$ParametersFile = ".\mg-hierarchy-arm-template.parameters.json",
    [switch]$WhatIf
)

Write-Host "Deploying Management Group Hierarchy ARM Template" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Check if files exist
if (-not (Test-Path $TemplateFile)) {
    Write-Error "Template file not found: $TemplateFile"
    exit 1
}

if (-not (Test-Path $ParametersFile)) {
    Write-Error "Parameters file not found: $ParametersFile"
    exit 1
}

# Import required modules
Import-Module Az.Accounts -Force
Import-Module Az.Resources -Force

# Check Azure context
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Generate deployment name
$deploymentName = "mg-hierarchy-deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

Write-Host "`nDeployment Configuration:" -ForegroundColor Green
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor White
Write-Host "  Location: $Location" -ForegroundColor White
Write-Host "  Template File: $TemplateFile" -ForegroundColor White
Write-Host "  Parameters File: $ParametersFile" -ForegroundColor White
Write-Host "  What-If Mode: $($WhatIf.IsPresent)" -ForegroundColor White

# Deployment parameters
$deployParams = @{
    Name = $deploymentName
    Location = $Location
    TemplateFile = $TemplateFile
    TemplateParameterFile = $ParametersFile
}

if ($WhatIf) {
    Write-Host "`nRunning What-If deployment..." -ForegroundColor Yellow
    try {
        $whatIfResult = New-AzTenantDeployment @deployParams -WhatIf
        Write-Host "`nWhat-If deployment completed successfully!" -ForegroundColor Green
        Write-Host "Review the changes above before running the actual deployment." -ForegroundColor Yellow
    }
    catch {
        Write-Error "What-If deployment failed: $($_.Exception.Message)"
        exit 1
    }
}
else {
    Write-Host "`nStarting deployment..." -ForegroundColor Yellow
    Write-Host "WARNING: This will create/modify Management Groups and Policy Assignments!" -ForegroundColor Red
    
    $confirmation = Read-Host "Do you want to continue? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-Host "Deployment cancelled by user." -ForegroundColor Yellow
        exit 0
    }
    
    try {
        $deployment = New-AzTenantDeployment @deployParams
        
        if ($deployment.ProvisioningState -eq "Succeeded") {
            Write-Host "`nDeployment completed successfully!" -ForegroundColor Green
            Write-Host "  Deployment Name: $($deployment.DeploymentName)" -ForegroundColor White
            Write-Host "  Provisioning State: $($deployment.ProvisioningState)" -ForegroundColor White
            Write-Host "  Duration: $($deployment.Duration)" -ForegroundColor White
        }
        else {
            Write-Error "Deployment failed with state: $($deployment.ProvisioningState)"
            if ($deployment.Error) {
                Write-Host "Error details: $($deployment.Error.Message)" -ForegroundColor Red
            }
            exit 1
        }
    }
    catch {
        Write-Error "Deployment failed: $($_.Exception.Message)"
        exit 1
    }
}

Write-Host "`nDeployment process completed!" -ForegroundColor Green
