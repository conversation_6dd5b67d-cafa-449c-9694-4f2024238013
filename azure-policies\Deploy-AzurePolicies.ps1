<#
.SYNOPSIS
    Deploy Complete Azure Policy Module for Azure Landing Zone

.DESCRIPTION
    This script deploys the complete Azure Policy module including:
    - Custom policy definitions
    - Policy initiatives (sets)
    - Policy assignments to management groups
    
    Supports both full deployment and individual component deployment.

.PARAMETER EnterpriseScaleCompanyPrefix
    Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy.

.PARAMETER DeploymentMode
    Deployment mode. Options: Full, Definitions, Initiatives, Assignments

.PARAMETER PolicyTypes
    Policy types to deploy for assignments. Options: Security, Network, DataProtection, All

.PARAMETER ManagementGroupMappings
    Hashtable mapping policy types to management group IDs for assignments.

.PARAMETER Effect
    Default effect for policy assignments. Options: Audit, Deny, Disabled

.PARAMETER WhatIf
    Preview the deployment without making changes.

.EXAMPLE
    .\Deploy-AzurePolicies.ps1 -EnterpriseScaleCompanyPrefix "ewh" -DeploymentMode "Full"

.EXAMPLE
    $mgMappings = @{
        "Security" = "ewh-Platform-Management"
        "Network" = "ewh-Platform-Connectivity" 
        "DataProtection" = "ewh"
    }
    .\Deploy-AzurePolicies.ps1 -EnterpriseScaleCompanyPrefix "ewh" -DeploymentMode "Assignments" -ManagementGroupMappings $mgMappings

.NOTES
    Author: Azure Landing Zone Team
    Version: 1.0.0
    Requires: Az.Accounts, Az.Resources modules
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [ValidateSet("Full", "Definitions", "Initiatives", "Assignments")]
    [string]$DeploymentMode = "Full",

    [Parameter(Mandatory = $false)]
    [ValidateSet("Security", "Network", "DataProtection", "All")]
    [string]$PolicyTypes = "All",

    [Parameter(Mandatory = $false)]
    [hashtable]$ManagementGroupMappings = @{},

    [Parameter(Mandatory = $false)]
    [ValidateSet("Audit", "Deny", "Disabled")]
    [string]$Effect = "Deny",

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "🚀 Azure Policy Module Deployment" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Get script path
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition

# Default management group mappings if not provided
if ($ManagementGroupMappings.Count -eq 0) {
    $ManagementGroupMappings = @{
        "Security" = "mg-Platform-Management"
        "Network" = "mg-Platform-Connectivity"
        "DataProtection" = $EnterpriseScaleCompanyPrefix
    }
}

Write-Host "`nDeployment Configuration:" -ForegroundColor Green
Write-Host "  Mode: $DeploymentMode" -ForegroundColor White
Write-Host "  Policy Types: $PolicyTypes" -ForegroundColor White
Write-Host "  Effect: $Effect" -ForegroundColor White
Write-Host "  Management Group Mappings:" -ForegroundColor White
foreach ($mapping in $ManagementGroupMappings.GetEnumerator()) {
    Write-Host "    $($mapping.Key) -> $($mapping.Value)" -ForegroundColor Gray
}

$deploymentResults = @{
    "Definitions" = $null
    "Initiatives" = $null
    "Assignments" = @()
}

# Step 1: Deploy Policy Definitions
if ($DeploymentMode -eq "Full" -or $DeploymentMode -eq "Definitions") {
    Write-Host "`n📋 Step 1: Deploying Policy Definitions..." -ForegroundColor Cyan
    
    $definitionsScript = Join-Path $scriptPath "policy-definitions\Deploy-PolicyDefinitions.ps1"
    if (Test-Path $definitionsScript) {
        try {
            $params = @{
                EnterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
            }
            if ($WhatIf) { $params.WhatIf = $true }
            
            & $definitionsScript @params
            $deploymentResults.Definitions = "Success"
            Write-Host "✅ Policy definitions deployment completed!" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Policy definitions deployment failed: $($_.Exception.Message)" -ForegroundColor Red
            $deploymentResults.Definitions = "Failed"
            if ($DeploymentMode -eq "Full") {
                Write-Host "Continuing with next step..." -ForegroundColor Yellow
            }
        }
    }
    else {
        Write-Warning "Policy definitions script not found: $definitionsScript"
        $deploymentResults.Definitions = "Skipped"
    }
}

# Step 2: Deploy Policy Initiatives
if ($DeploymentMode -eq "Full" -or $DeploymentMode -eq "Initiatives") {
    Write-Host "`n📦 Step 2: Deploying Policy Initiatives..." -ForegroundColor Cyan
    
    $initiativesScript = Join-Path $scriptPath "policy-initiatives\Deploy-PolicyInitiatives.ps1"
    if (Test-Path $initiativesScript) {
        try {
            $params = @{
                EnterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
            }
            if ($WhatIf) { $params.WhatIf = $true }
            
            & $initiativesScript @params
            $deploymentResults.Initiatives = "Success"
            Write-Host "✅ Policy initiatives deployment completed!" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Policy initiatives deployment failed: $($_.Exception.Message)" -ForegroundColor Red
            $deploymentResults.Initiatives = "Failed"
            if ($DeploymentMode -eq "Full") {
                Write-Host "Continuing with next step..." -ForegroundColor Yellow
            }
        }
    }
    else {
        Write-Warning "Policy initiatives script not found: $initiativesScript"
        $deploymentResults.Initiatives = "Skipped"
    }
}

# Step 3: Deploy Policy Assignments
if ($DeploymentMode -eq "Full" -or $DeploymentMode -eq "Assignments") {
    Write-Host "`n🎯 Step 3: Deploying Policy Assignments..." -ForegroundColor Cyan
    
    $assignmentsScript = Join-Path $scriptPath "policy-assignments\Deploy-PolicyAssignments.ps1"
    if (Test-Path $assignmentsScript) {
        # Determine which policy types to deploy
        $typesToDeploy = if ($PolicyTypes -eq "All") { 
            @("Security", "Network", "DataProtection") 
        } else { 
            @($PolicyTypes) 
        }
        
        foreach ($policyType in $typesToDeploy) {
            if ($ManagementGroupMappings.ContainsKey($policyType)) {
                $targetMG = $ManagementGroupMappings[$policyType]
                Write-Host "  Deploying $policyType policies to $targetMG..." -ForegroundColor Yellow
                
                try {
                    $params = @{
                        EnterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
                        PolicyType = $policyType
                        ManagementGroupId = $targetMG
                        Effect = $Effect
                    }
                    if ($WhatIf) { $params.WhatIf = $true }
                    
                    & $assignmentsScript @params
                    $deploymentResults.Assignments += @{
                        PolicyType = $policyType
                        ManagementGroup = $targetMG
                        Status = "Success"
                    }
                    Write-Host "  ✅ $policyType policy assignment completed!" -ForegroundColor Green
                }
                catch {
                    Write-Host "  ❌ $policyType policy assignment failed: $($_.Exception.Message)" -ForegroundColor Red
                    $deploymentResults.Assignments += @{
                        PolicyType = $policyType
                        ManagementGroup = $targetMG
                        Status = "Failed"
                        Error = $_.Exception.Message
                    }
                }
            }
            else {
                Write-Warning "No management group mapping found for policy type: $policyType"
                $deploymentResults.Assignments += @{
                    PolicyType = $policyType
                    Status = "Skipped"
                    Error = "No management group mapping"
                }
            }
        }
    }
    else {
        Write-Warning "Policy assignments script not found: $assignmentsScript"
    }
}

# Final Summary
Write-Host "`n📊 Deployment Summary" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

if ($deploymentResults.Definitions) {
    $status = if ($deploymentResults.Definitions -eq "Success") { "✅" } else { "❌" }
    Write-Host "$status Policy Definitions: $($deploymentResults.Definitions)" -ForegroundColor White
}

if ($deploymentResults.Initiatives) {
    $status = if ($deploymentResults.Initiatives -eq "Success") { "✅" } else { "❌" }
    Write-Host "$status Policy Initiatives: $($deploymentResults.Initiatives)" -ForegroundColor White
}

if ($deploymentResults.Assignments.Count -gt 0) {
    Write-Host "📋 Policy Assignments:" -ForegroundColor White
    foreach ($assignment in $deploymentResults.Assignments) {
        $status = switch ($assignment.Status) {
            "Success" { "✅" }
            "Failed" { "❌" }
            "Skipped" { "⏭️" }
        }
        Write-Host "  $status $($assignment.PolicyType) -> $($assignment.ManagementGroup): $($assignment.Status)" -ForegroundColor White
        if ($assignment.Error) {
            Write-Host "    Error: $($assignment.Error)" -ForegroundColor Red
        }
    }
}

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Monitor policy compliance in Azure Portal" -ForegroundColor White
Write-Host "2. Review policy evaluation results: .\Deploy-Complete-Module.ps1 -EnterpriseScaleCompanyPrefix "ewh" -DeploymentMode "All" -WhatIf" -ForegroundColor White
Write-Host "3. Adjust policy parameters as needed" -ForegroundColor White
Write-Host "4. Consider deploying workload-specific policies" -ForegroundColor White

Write-Host "`n🏁 Azure Policy Module deployment completed!" -ForegroundColor Green
