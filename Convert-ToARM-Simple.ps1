# Simple ARM Template Converter
param(
    [string]$InputJsonPath = ".\exported-templates\mg-hierarchy-demo-ewh.json",
    [string]$OutputPath = ".\exported-templates",
    [string]$TemplatePrefix = "demo-ewh"
)

Write-Host "Converting Management Group data to ARM Template..." -ForegroundColor Green

# Read the JSON data
if (-not (Test-Path $InputJsonPath)) {
    Write-Error "Input file not found: $InputJsonPath"
    exit 1
}

$data = Get-Content -Path $InputJsonPath -Raw | ConvertFrom-Json
$hierarchy = $data.ManagementGroupHierarchy

Write-Host "Processing hierarchy data..." -ForegroundColor Yellow

# Function to create MG resources recursively
function Create-MgResources {
    param($MgInfo, $Resources = @())
    
    # Create management group resource
    $mgResource = @{
        type = "Microsoft.Management/managementGroups"
        apiVersion = "2021-04-01"
        name = $MgInfo.Id
        properties = @{
            displayName = $MgInfo.DisplayName
        }
    }
    
    # Add parent if exists
    if ($MgInfo.ParentId) {
        $parentName = ($MgInfo.ParentId -split '/')[-1]
        $mgResource.properties.details = @{
            parent = @{
                id = $MgInfo.ParentId
            }
        }
        $mgResource.dependsOn = @("[resourceId('Microsoft.Management/managementGroups', '$parentName')]")
    }
    
    $Resources += $mgResource
    
    # Create policy assignments with unique names
    foreach ($policy in $MgInfo.PolicyAssignments) {
        if ($policy.Name) {
            # Create unique name by combining policy name with MG ID
            $uniquePolicyName = "$($policy.Name)-$($MgInfo.Id)"

            $policyResource = @{
                type = "Microsoft.Authorization/policyAssignments"
                apiVersion = "2022-06-01"
                name = $uniquePolicyName
                properties = @{
                    displayName = if ($policy.DisplayName) { $policy.DisplayName } else { $policy.Name }
                    policyDefinitionId = $policy.PolicyDefinitionId
                    scope = "/providers/Microsoft.Management/managementGroups/$($MgInfo.Id)"
                    enforcementMode = if ($policy.EnforcementMode) { $policy.EnforcementMode } else { "Default" }
                }
                dependsOn = @("[resourceId('Microsoft.Management/managementGroups', '$($MgInfo.Id)')]")
            }

            if ($policy.Parameters) {
                $policyResource.properties.parameters = $policy.Parameters
            }

            $Resources += $policyResource
        }
    }
    
    # Process children
    foreach ($child in $MgInfo.Children) {
        $Resources = Create-MgResources -MgInfo $child -Resources $Resources
    }
    
    return $Resources
}

# Generate ARM template
$template = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#"
    contentVersion = "*******"
    metadata = @{
        description = "Management Group hierarchy and Policy assignments for $TemplatePrefix"
        author = "Generated from existing Azure resources"
        generatedDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            type = "string"
            defaultValue = $TemplatePrefix
            maxLength = 10
            metadata = @{
                description = "Company prefix for Management Group hierarchy"
            }
        }
    }
    variables = @{}
    resources = @()
}

Write-Host "Creating ARM template resources..." -ForegroundColor Yellow
$resources = Create-MgResources -MgInfo $hierarchy
$template.resources = $resources

# Save ARM template
$templatePath = Join-Path $OutputPath "mg-hierarchy-arm-template.json"
$template | ConvertTo-Json -Depth 20 | Out-File -FilePath $templatePath -Encoding UTF8

Write-Host "ARM template saved to: $templatePath" -ForegroundColor Green

# Create parameters file
$parameters = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = $TemplatePrefix
        }
    }
}

$parametersPath = Join-Path $OutputPath "mg-hierarchy-arm-template.parameters.json"
$parameters | ConvertTo-Json -Depth 10 | Out-File -FilePath $parametersPath -Encoding UTF8

Write-Host "Parameters file saved to: $parametersPath" -ForegroundColor Green

# Summary
$mgCount = ($resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }).Count
$policyCount = ($resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }).Count

Write-Host "`nARM Template Summary:" -ForegroundColor Cyan
Write-Host "  Management Groups: $mgCount" -ForegroundColor White
Write-Host "  Policy Assignments: $policyCount" -ForegroundColor White
Write-Host "  Template File: $templatePath" -ForegroundColor White
Write-Host "  Parameters File: $parametersPath" -ForegroundColor White

Write-Host "`nDeployment Command:" -ForegroundColor Yellow
Write-Host "New-AzTenantDeployment -Location 'East US' -TemplateFile '$templatePath' -TemplateParameterFile '$parametersPath'" -ForegroundColor Cyan

Write-Host "`nConversion completed successfully!" -ForegroundColor Green
