param(
    [Parameter(Mandatory = $true)]
    [string]$EnterpriseScaleCompanyPrefix,
    
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

Write-Host "Testing Azure Policy Definitions deployment..." -ForegroundColor Green
Write-Host "Management Group: $EnterpriseScaleCompanyPrefix" -ForegroundColor White

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$templateFile = Join-Path $scriptPath "policies.json"

if (Test-Path $templateFile) {
    Write-Host "Template file found: $templateFile" -ForegroundColor Green
} else {
    Write-Host "Template file not found: $templateFile" -ForegroundColor Red
    exit 1
}

try {
    $context = Get-AzContext
    if ($context) {
        Write-Host "Azure context found" -ForegroundColor Green
        Write-Host "Subscription: $($context.Subscription.Name)" -ForegroundColor White
    } else {
        Write-Host "No Azure context found. Please run Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Azure PowerShell modules not available" -ForegroundColor Red
    exit 1
}

if ($WhatIf) {
    Write-Host "What-If mode - would deploy to: $EnterpriseScaleCompanyPrefix" -ForegroundColor Yellow
} else {
    Write-Host "Ready to deploy to: $EnterpriseScaleCompanyPrefix" -ForegroundColor Cyan
}

Write-Host "Test completed successfully!" -ForegroundColor Green
