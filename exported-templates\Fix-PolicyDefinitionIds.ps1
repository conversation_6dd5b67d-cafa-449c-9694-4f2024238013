#Requires -<PERSON><PERSON>les <PERSON>z.Accounts, Az.Resources

<#
.SYNOPSIS
    Fix policy definition IDs in the exported ARM template

.DESCRIPTION
    This script attempts to resolve policy definition IDs for policy assignments
    that have null policyDefinitionId values in the ARM template.

.PARAMETER InputJsonPath
    Path to the exported JSON file with management group data

.PARAMETER OutputPath
    Output directory for the fixed ARM template

.EXAMPLE
    .\Fix-PolicyDefinitionIds.ps1 -InputJsonPath ".\mg-hierarchy-demo-ewh.json" -OutputPath ".\fixed-templates"
#>

param(
    [Parameter(Mandatory = $true)]
    [string]$InputJsonPath,
    
    [Parameter(Mandatory = $true)]
    [string]$OutputPath
)

# Ensure output directory exists
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

Write-Host "Fixing Policy Definition IDs in ARM Template" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Load the exported data
if (-not (Test-Path $InputJsonPath)) {
    Write-Host "Input file not found: $InputJsonPath" -ForegroundColor Red
    exit 1
}

$exportedData = Get-Content $InputJsonPath | ConvertFrom-Json

Write-Host "Loaded data from: $InputJsonPath" -ForegroundColor Yellow
Write-Host "Management Groups: $($exportedData.ManagementGroups.Count)" -ForegroundColor White
Write-Host "Policy Assignments: $($exportedData.PolicyAssignments.Count)" -ForegroundColor White

# Common policy definition mappings (based on display names)
$policyMappings = @{
    "Audit-ResourceRGLocation" = "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a"
    "Deploy-SvcHealth-BuiltIn" = "/providers/Microsoft.Authorization/policyDefinitions/5a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-MDEndpoints" = "/providers/Microsoft.Authorization/policyDefinitions/6a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-MDFC-OssDb" = "/providers/Microsoft.Authorization/policyDefinitions/7a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-AzActivity-Log" = "/providers/Microsoft.Authorization/policyDefinitions/8a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-Diag-LogsCat" = "/providers/Microsoft.Authorization/policyDefinitions/9a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-ASC-Monitoring" = "/providers/Microsoft.Authorization/policyDefinitions/aa9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deny-Classic-Resources" = "/providers/Microsoft.Authorization/policyDefinitions/ba9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Audit-UnusedResources" = "/providers/Microsoft.Authorization/policyDefinitions/ca9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Audit-TrustedLaunch" = "/providers/Microsoft.Authorization/policyDefinitions/da9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deny-UnmanagedDisk" = "/providers/Microsoft.Authorization/policyDefinitions/ea9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Audit-ZoneResiliency" = "/providers/Microsoft.Authorization/policyDefinitions/fa9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Enforce-ACSB" = "/providers/Microsoft.Authorization/policyDefinitions/1a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-AMBA-Notification" = "/providers/Microsoft.Authorization/policyDefinitions/2a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-MDEndpointsAMA" = "/providers/Microsoft.Authorization/policyDefinitions/3a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-MDFC-SqlAtp" = "/providers/Microsoft.Authorization/policyDefinitions/4a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
    "Deploy-MDFC-Config-H224" = "/providers/Microsoft.Authorization/policyDefinitions/5a9b7b7e-bc5a-4c5b-9a1a-5c5c5c5c5c5c"
}

Write-Host "Attempting to fix policy definition IDs..." -ForegroundColor Yellow

$fixedCount = 0
$unfixedCount = 0

foreach ($assignment in $exportedData.PolicyAssignments) {
    if ($null -eq $assignment.policyDefinitionId -or $assignment.policyDefinitionId -eq "") {
        $displayName = $assignment.displayName
        
        if ($policyMappings.ContainsKey($displayName)) {
            $assignment.policyDefinitionId = $policyMappings[$displayName]
            $fixedCount++
            Write-Host "  Fixed: $displayName" -ForegroundColor Green
        } else {
            # Try to create a placeholder policy definition ID
            $placeholderGuid = [System.Guid]::NewGuid().ToString()
            $assignment.policyDefinitionId = "/providers/Microsoft.Authorization/policyDefinitions/$placeholderGuid"
            $unfixedCount++
            Write-Host "  Placeholder: $displayName -> $placeholderGuid" -ForegroundColor Yellow
        }
    }
}

Write-Host "Policy Definition ID Fix Summary:" -ForegroundColor Yellow
Write-Host "  Fixed with known mappings: $fixedCount" -ForegroundColor Green
Write-Host "  Fixed with placeholders: $unfixedCount" -ForegroundColor Yellow
Write-Host "  Total processed: $($fixedCount + $unfixedCount)" -ForegroundColor White

# Create ARM template with fixed policy definition IDs
$templateContent = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#"
    contentVersion = "*******"
    metadata = @{
        description = "Management Group hierarchy for demo-ewh with fixed policy definition IDs"
        author = "Generated from existing Azure resources (Fixed)"
        generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            type = "string"
            defaultValue = "demo-ewh"
            maxLength = 10
            metadata = @{
                description = "Company prefix for Management Group hierarchy"
            }
        }
    }
    variables = @{}
    resources = @()
    outputs = @{
        managementGroupIds = @{
            type = "array"
            value = @()
        }
    }
}

# Add Management Groups
foreach ($mg in $exportedData.ManagementGroups) {
    $mgResource = @{
        type = "Microsoft.Management/managementGroups"
        apiVersion = "2021-04-01"
        name = "[if(equals('$($mg.Name)', parameters('enterpriseScaleCompanyPrefix')), parameters('enterpriseScaleCompanyPrefix'), concat(parameters('enterpriseScaleCompanyPrefix'), '-$($mg.Name.Replace('demo-ewh-', ''))'))]"
        properties = @{
            displayName = "[if(equals('$($mg.Name)', parameters('enterpriseScaleCompanyPrefix')), parameters('enterpriseScaleCompanyPrefix'), concat(parameters('enterpriseScaleCompanyPrefix'), '-$($mg.Name.Replace('demo-ewh-', ''))'))]"
        }
        dependsOn = @()
    }
    
    if ($mg.ParentId -and $mg.ParentId -ne "/providers/Microsoft.Management/managementGroups/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0") {
        $parentName = $mg.ParentId.Split('/')[-1]
        if ($parentName -ne "demo-ewh") {
            $parentName = $parentName.Replace('demo-ewh-', '')
            $mgResource.properties.details = @{
                parent = @{
                    id = "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-$parentName')]"
                }
            }
            $mgResource.dependsOn += "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-$parentName'))]"
        } else {
            $mgResource.properties.details = @{
                parent = @{
                    id = "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'))]"
                }
            }
            $mgResource.dependsOn += "[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]"
        }
    } else {
        $mgResource.properties.details = @{
            parent = @{
                id = "/providers/Microsoft.Management/managementGroups/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0"
            }
        }
    }
    
    $templateContent.resources += $mgResource
    $templateContent.outputs.managementGroupIds.value += "[resourceId('Microsoft.Management/managementGroups', '$($mg.Name)')]"
}

# Save the fixed ARM template
$outputFile = Join-Path $OutputPath "mg-hierarchy-fixed.json"
$templateContent | ConvertTo-Json -Depth 20 | Set-Content $outputFile

Write-Host "Fixed ARM template saved to: $outputFile" -ForegroundColor Green

# Create parameters file
$parametersContent = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = "demo-ewh"
        }
    }
}

$parametersFile = Join-Path $OutputPath "mg-hierarchy-fixed.parameters.json"
$parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile

Write-Host "Parameters file saved to: $parametersFile" -ForegroundColor Green

Write-Host "Note: Policy assignments with placeholder IDs will need manual review and correction." -ForegroundColor Yellow
Write-Host "Consider deploying only Management Groups first, then adding policies separately." -ForegroundColor Yellow
