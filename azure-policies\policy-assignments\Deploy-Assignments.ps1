param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $true)]
    [ValidateSet("Security", "Network", "DataProtection", "All")]
    [string]$PolicyType,

    [Parameter(Mandatory = $true)]
    [string]$ManagementGroupId,

    [Parameter(Mandatory = $false)]
    [ValidateSet("Audit", "Deny", "Disabled")]
    [string]$Effect = "Deny",

    [Parameter(Mandatory = $false)]
    [ValidateSet("Default", "DoNotEnforce")]
    [string]$EnforcementMode = "Default",

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Get script path
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition

# Define policy assignment templates
$policyTemplates = @{
    "Security" = @{
        "TemplateFile" = Join-Path $scriptPath "security\security-baseline-assignment.json"
        "Name" = "Security Baseline"
    }
    "Network" = @{
        "TemplateFile" = Join-Path $scriptPath "networking\network-security-assignment.json"
        "Name" = "Network Security"
    }
    "DataProtection" = @{
        "TemplateFile" = Join-Path $scriptPath "compliance\data-protection-assignment.json"
        "Name" = "Data Protection"
    }
}

# Determine which policies to deploy
$policiesToDeploy = @()
if ($PolicyType -eq "All") {
    $policiesToDeploy = $policyTemplates.Keys
}
else {
    $policiesToDeploy = @($PolicyType)
}

Write-Host "`nPolicy Assignment Configuration:" -ForegroundColor Green
Write-Host "  Management Group: $ManagementGroupId" -ForegroundColor White
Write-Host "  Policy Type(s): $($policiesToDeploy -join ', ')" -ForegroundColor White
Write-Host "  Effect: $Effect" -ForegroundColor White
Write-Host "  Enforcement Mode: $EnforcementMode" -ForegroundColor White

# Deploy each policy type
$deploymentResults = @()

foreach ($policyType in $policiesToDeploy) {
    if (-not $policyTemplates.ContainsKey($policyType)) {
        Write-Warning "Policy type '$policyType' not found in templates"
        continue
    }

    $template = $policyTemplates[$policyType]
    $templateFile = $template.TemplateFile
    
    # Check if template file exists
    if (-not (Test-Path $templateFile)) {
        Write-Warning "Template file not found: $templateFile"
        continue
    }

    # Generate deployment name
    $deploymentName = "alz-policy-$($policyType.ToLower())-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

    # Prepare deployment parameters
    $deploymentParameters = @{
        Name                = $deploymentName
        ManagementGroupId   = $ManagementGroupId
        TemplateFile        = $templateFile
        Location            = "East Asia"
        topLevelManagementGroupPrefix = $EnterpriseScaleCompanyPrefix
        targetManagementGroupId = $ManagementGroupId
        effect = $Effect
        enforcementMode = $EnforcementMode
    }

    Write-Host "`nDeploying $($template.Name) Policy Assignment..." -ForegroundColor Cyan
    Write-Host "  Template: $templateFile" -ForegroundColor White
    Write-Host "  Deployment: $deploymentName" -ForegroundColor White

    try {
        if ($WhatIf) {
            Write-Host "  Running What-If analysis..." -ForegroundColor Yellow
            $result = New-AzManagementGroupDeployment @deploymentParameters -WhatIf
            Write-Host "  What-If analysis completed!" -ForegroundColor Green
        }
        else {
            Write-Host "  Deploying..." -ForegroundColor Yellow
            $result = New-AzManagementGroupDeployment @deploymentParameters
            
            if ($result.ProvisioningState -eq "Succeeded") {
                Write-Host "  $($template.Name) policy assigned successfully!" -ForegroundColor Green
                
                $deploymentResults += @{
                    PolicyType = $policyType
                    Status = "Success"
                }
            }
            else {
                Write-Host "  Deployment failed with state: $($result.ProvisioningState)" -ForegroundColor Red
                $deploymentResults += @{
                    PolicyType = $policyType
                    Status = "Failed"
                    Error = $result.ProvisioningState
                }
            }
        }
    }
    catch {
        Write-Host "  Failed to deploy $($template.Name): $($_.Exception.Message)" -ForegroundColor Red
        $deploymentResults += @{
            PolicyType = $policyType
            Status = "Error"
            Error = $_.Exception.Message
        }
    }
}

# Summary
Write-Host "`nDeployment Summary:" -ForegroundColor Cyan
foreach ($result in $deploymentResults) {
    $status = switch ($result.Status) {
        "Success" { "Success" }
        "Failed" { "Failed" }
        "Error" { "Error" }
    }
    Write-Host "  $($result.PolicyType): $status" -ForegroundColor White
    if ($result.Error) {
        Write-Host "    Error: $($result.Error)" -ForegroundColor Red
    }
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Monitor policy compliance in Azure Portal" -ForegroundColor White
Write-Host "2. View policy assignments: Get-AzPolicyAssignment" -ForegroundColor White
Write-Host "3. Check policy evaluation results" -ForegroundColor White
