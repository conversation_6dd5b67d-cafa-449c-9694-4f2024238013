<#
.SYNOPSIS
    Complete workflow to export Management Group hierarchy and policies to ARM template

.DESCRIPTION
    This script performs the complete workflow:
    1. Checks the existing management group hierarchy for demo-ewh
    2. Retrieves all policy assignments
    3. Exports the data to JSON
    4. Converts to ARM template format
    5. Creates deployment scripts

.PARAMETER ManagementGroupPrefix
    The management group prefix to check (default: demo-ewh)

.PARAMETER OutputPath
    Path to save all generated files (default: .\exported-templates)

.EXAMPLE
    .\Export-ManagementGroupToARM.ps1

.EXAMPLE
    .\Export-ManagementGroupToARM.ps1 -ManagementGroupPrefix "demo-ewh" -OutputPath "C:\temp\export"
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupPrefix = "demo-ewh",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".\exported-templates"
)

# Create output directory if it doesn't exist
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "📁 Created output directory: $OutputPath" -ForegroundColor Green
}

Write-Host "🚀 Management Group to ARM Template Export Workflow" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host "Management Group Prefix: $ManagementGroupPrefix" -ForegroundColor White
Write-Host "Output Path: $OutputPath" -ForegroundColor White

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources', 'Az.Profile')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "`nCurrent Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Step 1: Check Management Group Hierarchy
Write-Host "`n📊 Step 1: Checking Management Group Hierarchy..." -ForegroundColor Cyan

# Function to get management group hierarchy
function Get-ManagementGroupHierarchy {
    param(
        [string]$ManagementGroupId,
        [int]$Level = 0
    )
    
    try {
        $mg = Get-AzManagementGroup -GroupId $ManagementGroupId -Expand -Recurse
        
        $indent = "  " * $Level
        Write-Host "$indent📁 $($mg.DisplayName) ($($mg.Name))" -ForegroundColor Yellow
        
        $mgInfo = @{
            Id = $mg.Name
            DisplayName = $mg.DisplayName
            ParentId = $mg.ParentId
            Children = @()
            Subscriptions = @()
            Level = $Level
            PolicyAssignments = @()
        }
        
        # Get subscriptions in this management group
        if ($mg.Children) {
            foreach ($child in $mg.Children) {
                if ($child.Type -eq "/subscriptions") {
                    Write-Host "$indent  📋 Subscription: $($child.DisplayName) ($($child.Name))" -ForegroundColor Green
                    $mgInfo.Subscriptions += @{
                        Id = $child.Name
                        DisplayName = $child.DisplayName
                    }
                }
                elseif ($child.Type -eq "/providers/Microsoft.Management/managementGroups") {
                    $childInfo = Get-ManagementGroupHierarchy -ManagementGroupId $child.Name -Level ($Level + 1)
                    if ($childInfo) {
                        $mgInfo.Children += $childInfo
                    }
                }
            }
        }

        return $mgInfo
    }
    catch {
        Write-Warning "Could not retrieve management group: $ManagementGroupId. Error: $($_.Exception.Message)"
        return $null
    }
}

# Function to get policy assignments
function Get-PolicyAssignments {
    param(
        [string]$ManagementGroupId
    )
    
    try {
        $scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupId"
        $assignments = Get-AzPolicyAssignment -Scope $scope
        
        $assignmentInfo = @()
        foreach ($assignment in $assignments) {
            Write-Host "    🎯 Policy: $($assignment.Properties.DisplayName)" -ForegroundColor Cyan
            
            $assignmentInfo += @{
                Name = $assignment.Name
                DisplayName = $assignment.Properties.DisplayName
                PolicyDefinitionId = $assignment.Properties.PolicyDefinitionId
                Scope = $assignment.Properties.Scope
                Parameters = $assignment.Properties.Parameters
                EnforcementMode = $assignment.Properties.EnforcementMode
                Identity = $assignment.Identity
            }
        }
        
        return $assignmentInfo
    }
    catch {
        Write-Warning "Could not retrieve policy assignments for: $ManagementGroupId. Error: $($_.Exception.Message)"
        return @()
    }
}

# Get the hierarchy
$hierarchy = Get-ManagementGroupHierarchy -ManagementGroupId $ManagementGroupPrefix

if (-not $hierarchy) {
    Write-Error "Management group '$ManagementGroupPrefix' not found or not accessible."
    exit 1
}

# Step 2: Get Policy Assignments
Write-Host "`n🎯 Step 2: Retrieving Policy Assignments..." -ForegroundColor Cyan

function Get-AllPolicyAssignments {
    param($MgInfo)
    
    Write-Host "`nManagement Group: $($MgInfo.DisplayName) ($($MgInfo.Id))" -ForegroundColor Yellow
    $policies = Get-PolicyAssignments -ManagementGroupId $MgInfo.Id
    $MgInfo.PolicyAssignments = $policies
    
    foreach ($child in $MgInfo.Children) {
        Get-AllPolicyAssignments -MgInfo $child
    }
}

Get-AllPolicyAssignments -MgInfo $hierarchy

# Step 3: Export to JSON
Write-Host "`n💾 Step 3: Exporting to JSON..." -ForegroundColor Cyan

$jsonOutput = @{
    ManagementGroupHierarchy = $hierarchy
    ExportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    TenantId = $context.Tenant.Id
    SubscriptionId = $context.Subscription.Id
}

$jsonPath = Join-Path $OutputPath "management-group-hierarchy-$ManagementGroupPrefix.json"
$jsonOutput | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonPath -Encoding UTF8
Write-Host "✅ Hierarchy exported to: $jsonPath" -ForegroundColor Green

Write-Host "`n📋 Export Summary:" -ForegroundColor Green
Write-Host "  Management Group Prefix: $ManagementGroupPrefix" -ForegroundColor White
Write-Host "  Export Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "  JSON File: $jsonPath" -ForegroundColor White

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review the exported JSON file: $jsonPath" -ForegroundColor White
Write-Host "2. Run Convert-ToARMTemplate.ps1 to generate ARM template:" -ForegroundColor White
Write-Host "   .\Convert-ToARMTemplate.ps1 -InputJsonPath '$jsonPath' -OutputPath '$OutputPath'" -ForegroundColor Yellow
Write-Host "3. Deploy the ARM template to recreate the hierarchy" -ForegroundColor White

Write-Host "`n✅ Management Group export completed successfully!" -ForegroundColor Green

return @{
    HierarchyData = $hierarchy
    JsonPath = $jsonPath
    OutputPath = $OutputPath
}
