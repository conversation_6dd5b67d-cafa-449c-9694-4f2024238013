param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [string]$TemplateFile,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Set default template file path if not provided
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
if (-not $TemplateFile) {
    $TemplateFile = Join-Path $scriptPath "initiatives.json"
}

# Validate template file exists
if (-not (Test-Path $TemplateFile)) {
    Write-Error "Template file not found: $TemplateFile"
    exit 1
}

# Generate deployment name
$deploymentName = "alz-policy-initiatives-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# Prepare deployment parameters
$deploymentParameters = @{
    Name                = $deploymentName
    ManagementGroupId   = $EnterpriseScaleCompanyPrefix
    TemplateFile        = $TemplateFile
    Location            = "East Asia"
    topLevelManagementGroupPrefix = $EnterpriseScaleCompanyPrefix
}

Write-Host "`nPolicy Initiatives Deployment Configuration:" -ForegroundColor Green
Write-Host "  Management Group: $EnterpriseScaleCompanyPrefix" -ForegroundColor White
Write-Host "  Template File: $TemplateFile" -ForegroundColor White
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor White

try {
    if ($WhatIf) {
        Write-Host "`nRunning What-If analysis..." -ForegroundColor Yellow
        $result = New-AzManagementGroupDeployment @deploymentParameters -WhatIf
        Write-Host "What-If analysis completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "`nDeploying policy initiatives..." -ForegroundColor Yellow
        $result = New-AzManagementGroupDeployment @deploymentParameters
        
        if ($result.ProvisioningState -eq "Succeeded") {
            Write-Host "Policy initiatives deployed successfully!" -ForegroundColor Green
        }
        else {
            Write-Error "Deployment failed with state: $($result.ProvisioningState)"
        }
    }
}
catch {
    Write-Error "Failed to deploy policy initiatives: $($_.Exception.Message)"
    throw
}

Write-Host "`nPolicy initiatives deployment completed!" -ForegroundColor Green

# Verify deployment
Write-Host "`nVerifying policy initiatives..." -ForegroundColor Cyan
try {
    $initiatives = Get-AzPolicySetDefinition -ManagementGroupName $EnterpriseScaleCompanyPrefix | Where-Object { $_.Properties.PolicyType -eq "Custom" }
    Write-Host "Found $($initiatives.Count) custom policy initiative(s) in management group '$EnterpriseScaleCompanyPrefix'" -ForegroundColor Green
    
    foreach ($initiative in $initiatives) {
        Write-Host "  - $($initiative.Name): $($initiative.Properties.DisplayName)" -ForegroundColor White
        Write-Host "    Policies: $($initiative.Properties.PolicyDefinitions.Count)" -ForegroundColor Gray
    }
}
catch {
    Write-Warning "Could not verify policy initiatives: $($_.Exception.Message)"
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Assign policies to management groups: cd ../policy-assignments" -ForegroundColor White
Write-Host "2. Run assignment script with same prefix" -ForegroundColor White
