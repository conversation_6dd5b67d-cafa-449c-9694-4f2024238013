<#
.SYNOPSIS
    Check Management Group Hierarchy and Policy Assignments for demo-ewh

.DESCRIPTION
    This script checks the existing management group hierarchy for demo-ewh,
    retrieves all policy assignments, and generates an ARM template for recreation.

.PARAMETER ManagementGroupPrefix
    The management group prefix to check (default: demo-ewh)

.PARAMETER OutputPath
    Path to save the generated ARM template (default: current directory)

.PARAMETER ExportToJson
    Export the hierarchy and policies to JSON format

.EXAMPLE
    .\Check-ManagementGroupHierarchy.ps1 -ManagementGroupPrefix "demo-ewh"

.EXAMPLE
    .\Check-ManagementGroupHierarchy.ps1 -ManagementGroupPrefix "demo-ewh" -OutputPath "C:\temp" -ExportToJson
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupPrefix = "demo-ewh",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = ".",

    [Parameter(Mandatory = $false)]
    [switch]$ExportToJson
)

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources', 'Az.Profile')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "🔍 Checking Management Group Hierarchy for: $ManagementGroupPrefix" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Function to get management group hierarchy
function Get-ManagementGroupHierarchy {
    param(
        [string]$ManagementGroupId,
        [int]$Level = 0
    )
    
    try {
        $mg = Get-AzManagementGroup -GroupId $ManagementGroupId -Expand -Recurse
        
        $indent = "  " * $Level
        Write-Host "$indent📁 $($mg.DisplayName) ($($mg.Name))" -ForegroundColor Yellow
        
        $mgInfo = @{
            Id = $mg.Name
            DisplayName = $mg.DisplayName
            ParentId = $mg.ParentId
            Children = @()
            Subscriptions = @()
            Level = $Level
        }
        
        # Get subscriptions in this management group
        if ($mg.Children) {
            foreach ($child in $mg.Children) {
                if ($child.Type -eq "/subscriptions") {
                    Write-Host "$indent  📋 Subscription: $($child.DisplayName) ($($child.Name))" -ForegroundColor Green
                    $mgInfo.Subscriptions += @{
                        Id = $child.Name
                        DisplayName = $child.DisplayName
                    }
                }
                elseif ($child.Type -eq "/providers/Microsoft.Management/managementGroups") {
                    $childInfo = Get-ManagementGroupHierarchy -ManagementGroupId $child.Name -Level ($Level + 1)
                    $mgInfo.Children += $childInfo
                }
            }
        }
        
        return $mgInfo
    }
    catch {
        Write-Warning "Could not retrieve management group: $ManagementGroupId. Error: $($_.Exception.Message)"
        return $null
    }
}

# Function to get policy assignments for a management group
function Get-PolicyAssignments {
    param(
        [string]$ManagementGroupId
    )
    
    try {
        $scope = "/providers/Microsoft.Management/managementGroups/$ManagementGroupId"
        $assignments = Get-AzPolicyAssignment -Scope $scope
        
        $assignmentInfo = @()
        foreach ($assignment in $assignments) {
            Write-Host "    🎯 Policy: $($assignment.Properties.DisplayName)" -ForegroundColor Cyan
            Write-Host "       ID: $($assignment.Name)" -ForegroundColor Gray
            Write-Host "       Type: $($assignment.Properties.PolicyDefinitionId -split '/' | Select-Object -Last 1)" -ForegroundColor Gray
            
            $assignmentInfo += @{
                Name = $assignment.Name
                DisplayName = $assignment.Properties.DisplayName
                PolicyDefinitionId = $assignment.Properties.PolicyDefinitionId
                Scope = $assignment.Properties.Scope
                Parameters = $assignment.Properties.Parameters
                EnforcementMode = $assignment.Properties.EnforcementMode
                Identity = $assignment.Identity
            }
        }
        
        return $assignmentInfo
    }
    catch {
        Write-Warning "Could not retrieve policy assignments for: $ManagementGroupId. Error: $($_.Exception.Message)"
        return @()
    }
}

# Get the hierarchy starting from the root management group
Write-Host "`n📊 Management Group Hierarchy:" -ForegroundColor Cyan
$hierarchy = Get-ManagementGroupHierarchy -ManagementGroupId $ManagementGroupPrefix

if (-not $hierarchy) {
    Write-Error "Management group '$ManagementGroupPrefix' not found or not accessible."
    exit 1
}

# Get policy assignments for all management groups in the hierarchy
Write-Host "`n🎯 Policy Assignments:" -ForegroundColor Cyan

function Get-AllPolicyAssignments {
    param($MgInfo)
    
    Write-Host "`nManagement Group: $($MgInfo.DisplayName) ($($MgInfo.Id))" -ForegroundColor Yellow
    $policies = Get-PolicyAssignments -ManagementGroupId $MgInfo.Id
    $MgInfo.PolicyAssignments = $policies
    
    foreach ($child in $MgInfo.Children) {
        Get-AllPolicyAssignments -MgInfo $child
    }
}

Get-AllPolicyAssignments -MgInfo $hierarchy

# Export to JSON if requested
if ($ExportToJson) {
    $jsonOutput = @{
        ManagementGroupHierarchy = $hierarchy
        ExportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        TenantId = $context.Tenant.Id
        SubscriptionId = $context.Subscription.Id
    }
    
    $jsonPath = Join-Path $OutputPath "management-group-hierarchy-$ManagementGroupPrefix.json"
    $jsonOutput | ConvertTo-Json -Depth 10 | Out-File -FilePath $jsonPath -Encoding UTF8
    Write-Host "`n💾 Hierarchy exported to: $jsonPath" -ForegroundColor Green
}

Write-Host "`n✅ Management Group hierarchy check completed!" -ForegroundColor Green
return $hierarchy
