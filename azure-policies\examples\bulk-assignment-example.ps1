# Bulk Policy Assignment Example for Azure Landing Zone
# This script demonstrates how to assign multiple policies across different management groups

param(
    [Parameter(Mandatory = $true)]
    [string]$EnterpriseScaleCompanyPrefix
)

# Define management group structure
$managementGroups = @{
    "Root" = $EnterpriseScaleCompanyPrefix
    "Platform" = "$EnterpriseScaleCompanyPrefix-Platform"
    "Management" = "$EnterpriseScaleCompanyPrefix-Platform-Management"
    "Connectivity" = "$EnterpriseScaleCompanyPrefix-Platform-Connectivity"
    "LandingZones" = "$EnterpriseScaleCompanyPrefix-LandingZone"
    "Production" = "$EnterpriseScaleCompanyPrefix-ldz-prd"
    "NonProduction" = "$EnterpriseScaleCompanyPrefix-ldz-non-prd"
    "Sandbox" = "$EnterpriseScaleCompanyPrefix-Sandbox"
    "Decommissioned" = "$EnterpriseScaleCompanyPrefix-Decommissioned"
}

# Define policy assignment mappings
$policyAssignments = @(
    # Platform Management Group Policies
    @{
        PolicyType = "Security"
        ManagementGroup = $managementGroups.Management
        Effect = "Deny"
        Description = "Security baseline for platform management"
    },
    @{
        PolicyType = "Network"
        ManagementGroup = $managementGroups.Connectivity
        Effect = "Deny"
        Description = "Network security for connectivity resources"
    },
    
    # Landing Zone Policies
    @{
        PolicyType = "Security"
        ManagementGroup = $managementGroups.Production
        Effect = "Deny"
        Description = "Security baseline for production workloads"
    },
    @{
        PolicyType = "DataProtection"
        ManagementGroup = $managementGroups.Production
        Effect = "Deny"
        Description = "Data protection for production workloads"
    },
    @{
        PolicyType = "Security"
        ManagementGroup = $managementGroups.NonProduction
        Effect = "Audit"
        Description = "Security baseline for non-production workloads"
    },
    @{
        PolicyType = "DataProtection"
        ManagementGroup = $managementGroups.NonProduction
        Effect = "Audit"
        Description = "Data protection for non-production workloads"
    },
    
    # Sandbox Policies (Relaxed)
    @{
        PolicyType = "Security"
        ManagementGroup = $managementGroups.Sandbox
        Effect = "Audit"
        Description = "Relaxed security for sandbox environments"
    },
    
    # Decommissioned Policies (Strict)
    @{
        PolicyType = "Security"
        ManagementGroup = $managementGroups.Decommissioned
        Effect = "Deny"
        Description = "Strict security for decommissioned resources"
    }
)

# Deploy policies
Write-Host "🚀 Starting bulk policy assignment deployment..." -ForegroundColor Cyan

foreach ($assignment in $policyAssignments) {
    Write-Host "`n📋 Assigning $($assignment.PolicyType) policies to $($assignment.ManagementGroup)..." -ForegroundColor Yellow
    
    try {
        $params = @{
            EnterpriseScaleCompanyPrefix = $EnterpriseScaleCompanyPrefix
            PolicyType = $assignment.PolicyType
            ManagementGroupId = $assignment.ManagementGroup
            Effect = $assignment.Effect
        }
        
        & ".\policy-assignments\Deploy-PolicyAssignments.ps1" @params
        
        Write-Host "✅ Successfully assigned $($assignment.PolicyType) policies to $($assignment.ManagementGroup)" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to assign $($assignment.PolicyType) policies to $($assignment.ManagementGroup): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Deploy workload-specific policies
Write-Host "`n🔧 Deploying workload-specific policies..." -ForegroundColor Cyan

$workloadPolicies = @(
    @{
        Workload = "Storage"
        ManagementGroups = @($managementGroups.Production, $managementGroups.NonProduction)
        Effect = "Deny"
    },
    @{
        Workload = "SQL"
        ManagementGroups = @($managementGroups.Production)
        Effect = "Deny"
    },
    @{
        Workload = "Kubernetes"
        ManagementGroups = @($managementGroups.Production, $managementGroups.NonProduction)
        Effect = "Deny"
    }
)

foreach ($workload in $workloadPolicies) {
    foreach ($mg in $workload.ManagementGroups) {
        Write-Host "📦 Deploying $($workload.Workload) policies to $mg..." -ForegroundColor Yellow
        
        try {
            # Deploy workload-specific policy (example for storage)
            if ($workload.Workload -eq "Storage") {
                $params = @{
                    Name = "alz-storage-security-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                    ManagementGroupId = $EnterpriseScaleCompanyPrefix
                    TemplateFile = ".\workload-specific\storage\storage-security-assignment.json"
                    topLevelManagementGroupPrefix = $EnterpriseScaleCompanyPrefix
                    targetManagementGroupId = $mg
                    effect = $workload.Effect
                }
                
                New-AzManagementGroupDeployment @params
            }
            
            Write-Host "✅ Successfully deployed $($workload.Workload) policies to $mg" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Failed to deploy $($workload.Workload) policies to $mg`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n🏁 Bulk policy assignment deployment completed!" -ForegroundColor Green
Write-Host "`n📊 Summary:" -ForegroundColor Cyan
Write-Host "- Assigned policies to $($policyAssignments.Count) management groups" -ForegroundColor White
Write-Host "- Deployed workload-specific policies for $($workloadPolicies.Count) workload types" -ForegroundColor White
Write-Host "`n🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Monitor policy compliance in Azure Portal" -ForegroundColor White
Write-Host "2. Review and adjust policy parameters as needed" -ForegroundColor White
Write-Host "3. Set up policy remediation tasks for non-compliant resources" -ForegroundColor White
