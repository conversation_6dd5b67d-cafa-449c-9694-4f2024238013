{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}}, "variables": {}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "Deny-Classic-Resources", "properties": {"displayName": "Deny deployment of classic resources", "description": "This policy denies deployment of classic compute, network and storage resources", "policyType": "Custom", "mode": "All", "metadata": {"category": "General", "source": "Azure Landing Zone"}, "policyRule": {"if": {"anyOf": [{"field": "type", "equals": "Microsoft.ClassicCompute/virtualMachines"}, {"field": "type", "equals": "Microsoft.ClassicNetwork/virtualNetworks"}, {"field": "type", "equals": "Microsoft.ClassicStorage/storageAccounts"}]}, "then": {"effect": "deny"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "Require-Storage-Https", "properties": {"displayName": "Require HTTPS for storage accounts", "description": "This policy requires HTTPS for storage account access", "policyType": "Custom", "mode": "All", "metadata": {"category": "Storage", "source": "Azure Landing Zone"}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "notEquals": "true"}]}, "then": {"effect": "deny"}}}}], "outputs": {"policyDefinitionIds": {"type": "object", "value": {"denyClassicResources": "[resourceId('Microsoft.Authorization/policyDefinitions', 'Deny-Classic-Resources')]", "requireStorageHttps": "[resourceId('Microsoft.Authorization/policyDefinitions', 'Require-Storage-Https')]"}}}}