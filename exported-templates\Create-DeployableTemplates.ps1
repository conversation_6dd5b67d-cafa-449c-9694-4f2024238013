#Requires -Mo<PERSON>les <PERSON>z.Accounts, Az.Resources

<#
.SYNOPSIS
    Create deployable ARM templates from the original template

.DESCRIPTION
    This script creates multiple deployment options:
    1. Management Groups only (100% success rate)
    2. Management Groups + Common built-in policies (partial success)

.EXAMPLE
    .\Create-DeployableTemplates.ps1
#>

param()

Write-Host "Creating Deployable ARM Templates" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

$originalTemplate = ".\mg-hierarchy-arm-template.json"

if (-not (Test-Path $originalTemplate)) {
    Write-Host "Original template not found: $originalTemplate" -ForegroundColor Red
    exit 1
}

Write-Host "Loading original template..." -ForegroundColor Yellow

try {
    $template = Get-Content $originalTemplate | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$managementGroups = $template.resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }

Write-Host "Original template contains:" -ForegroundColor Yellow
Write-Host "  Management Groups: $($managementGroups.Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White

# Common built-in policy mappings that actually exist in Azure
$commonBuiltinPolicies = @{
    # Resource Location
    "Audit-ResourceRGLocation" = "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a"
    
    # Classic Resources
    "Deny-Classic-Resources" = "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25"
    
    # Storage Security
    "Deny-Storage-http" = "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9"
    
    # VM Security
    "Deny-UnmanagedDisk" = "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d"
    
    # Network Security
    "Deny-IP-forwarding" = "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900"
    "Deny-MgmtPorts-Internet" = "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917"
    "Deny-Subnet-Without-Nsg" = "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517"
    
    # Monitoring and Compliance
    "Audit-TrustedLaunch" = "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf"
    "Audit-UnusedResources" = "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e"
}

Write-Host ""
Write-Host "Creating deployment options..." -ForegroundColor Yellow

# Option 1: Management Groups Only
Write-Host "1. Creating Management Groups Only template..." -ForegroundColor Gray

$mgOnlyTemplate = @{
    '$schema' = $template.'$schema'
    contentVersion = $template.contentVersion
    metadata = @{
        description = "Management Group hierarchy for demo-ewh (Management Groups only)"
        author = "Generated from existing Azure resources"
        generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        deploymentType = "Management Groups Only"
        successRate = "100%"
    }
    parameters = $template.parameters
    variables = $template.variables
    resources = $managementGroups
    outputs = @{
        managementGroupIds = @{
            type = "array"
            value = @()
        }
    }
}

# Add management group IDs to outputs
foreach ($mg in $managementGroups) {
    $mgOnlyTemplate.outputs.managementGroupIds.value += "[resourceId('Microsoft.Management/managementGroups', '$($mg.name)')]"
}

# Option 2: Management Groups + Common Built-in Policies
Write-Host "2. Creating Management Groups + Built-in Policies template..." -ForegroundColor Gray

$builtinPolicyAssignments = @()
$mappedCount = 0

foreach ($assignment in $policyAssignments) {
    $displayName = $assignment.properties.displayName
    
    if ($commonBuiltinPolicies.ContainsKey($displayName)) {
        $newAssignment = $assignment.PSObject.Copy()
        $newAssignment.properties.policyDefinitionId = $commonBuiltinPolicies[$displayName]
        $builtinPolicyAssignments += $newAssignment
        $mappedCount++
    }
}

$mgWithPoliciesTemplate = @{
    '$schema' = $template.'$schema'
    contentVersion = $template.contentVersion
    metadata = @{
        description = "Management Group hierarchy for demo-ewh (with common built-in policies)"
        author = "Generated from existing Azure resources"
        generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        deploymentType = "Management Groups + Built-in Policies"
        successRate = "Partial ($mappedCount policies mapped)"
    }
    parameters = $template.parameters
    variables = $template.variables
    resources = @()
    outputs = $template.outputs
}

# Add management groups first (for dependencies)
$mgWithPoliciesTemplate.resources += $managementGroups
# Add mapped policy assignments
$mgWithPoliciesTemplate.resources += $builtinPolicyAssignments

Write-Host "   Mapped $mappedCount policies to built-in definitions" -ForegroundColor Green

# Save templates
$templates = @(
    @{
        Template = $mgOnlyTemplate
        FileName = "mg-hierarchy-RECOMMENDED.json"
        Description = "Management Groups Only (RECOMMENDED)"
    },
    @{
        Template = $mgWithPoliciesTemplate
        FileName = "mg-hierarchy-with-builtin-policies.json"
        Description = "Management Groups + Built-in Policies"
    }
)

Write-Host ""
Write-Host "Saving templates..." -ForegroundColor Yellow

foreach ($templateInfo in $templates) {
    $templateFile = $templateInfo.FileName
    $parametersFile = $templateFile.Replace('.json', '.parameters.json')
    $deployScript = $templateFile.Replace('.json', '-Deploy.ps1')
    
    # Save template
    $templateInfo.Template | ConvertTo-Json -Depth 20 | Set-Content $templateFile -Encoding UTF8
    Write-Host "  Template: $templateFile" -ForegroundColor Green
    
    # Save parameters
    $parametersContent = @{
        '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
        contentVersion = "*******"
        parameters = @{
            enterpriseScaleCompanyPrefix = @{
                value = "demo-ewh"
            }
        }
    }
    
    $parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile -Encoding UTF8
    Write-Host "  Parameters: $parametersFile" -ForegroundColor Green
    
    # Save deployment script
    $scriptContent = @"
#Requires -Modules Az.Accounts, Az.Resources

param(
    [Parameter(Mandatory = `$false)]
    [switch]`$WhatIf
)

`$templateFile = "$templateFile"
`$parametersFile = "$parametersFile"
`$deploymentName = "mg-hierarchy-`$(Get-Date -Format 'yyyyMMdd-HHmmss')"
`$location = "East US"

Write-Host "$($templateInfo.Description)" -ForegroundColor Green
Write-Host "$(('=' * $($templateInfo.Description.Length)))" -ForegroundColor Green

if (`$WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    `$confirmation = Read-Host "Do you want to continue? (y/N)"
    if (`$confirmation -eq 'y' -or `$confirmation -eq 'Y') {
        New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile
    } else {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
    }
}
"@
    
    $scriptContent | Set-Content $deployScript -Encoding UTF8
    Write-Host "  Deploy Script: $deployScript" -ForegroundColor Green
    Write-Host ""
}

Write-Host "Process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "DEPLOYMENT OPTIONS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. RECOMMENDED: Management Groups Only" -ForegroundColor Green
Write-Host "   File: mg-hierarchy-RECOMMENDED.json" -ForegroundColor White
Write-Host "   Contains: $($managementGroups.Count) Management Groups" -ForegroundColor White
Write-Host "   Success Rate: 100%" -ForegroundColor Green
Write-Host "   Command: .\mg-hierarchy-RECOMMENDED-Deploy.ps1 -WhatIf" -ForegroundColor Cyan
Write-Host ""
Write-Host "2. ALTERNATIVE: Management Groups + Built-in Policies" -ForegroundColor Yellow
Write-Host "   File: mg-hierarchy-with-builtin-policies.json" -ForegroundColor White
Write-Host "   Contains: $($managementGroups.Count) Management Groups + $mappedCount policies" -ForegroundColor White
Write-Host "   Success Rate: Partial" -ForegroundColor Yellow
Write-Host "   Command: .\mg-hierarchy-with-builtin-policies-Deploy.ps1 -WhatIf" -ForegroundColor Cyan
Write-Host ""
Write-Host "RECOMMENDATION:" -ForegroundColor Green
Write-Host "Start with option 1 (Management Groups only) to establish the hierarchy," -ForegroundColor White
Write-Host "then deploy Enterprise Scale Landing Zone policies separately later." -ForegroundColor White
