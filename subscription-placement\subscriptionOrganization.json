{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Subscription Placement Module", "version": "1.0.0"}, "description": "This template places a subscription into a specific management group"}, "parameters": {"targetManagementGroupId": {"type": "string", "metadata": {"description": "The management group ID where the subscription should be placed"}}, "subscriptionId": {"type": "string", "metadata": {"description": "The subscription ID to be placed in the management group"}}}, "variables": {"subscriptionResourceId": "[concat('/subscriptions/', parameters('subscriptionId'))]"}, "resources": [{"type": "Microsoft.Management/managementGroups/subscriptions", "apiVersion": "2020-05-01", "name": "[concat(parameters('targetManagementGroupId'), '/', parameters('subscriptionId'))]", "properties": {}}], "outputs": {"subscriptionId": {"type": "string", "value": "[parameters('subscriptionId')]"}, "targetManagementGroupId": {"type": "string", "value": "[parameters('targetManagementGroupId')]"}, "subscriptionResourceId": {"type": "string", "value": "[variables('subscriptionResourceId')]"}}}