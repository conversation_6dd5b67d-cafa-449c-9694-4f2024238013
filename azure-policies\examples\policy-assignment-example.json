{"managementGroupMappings": {"Security": "ewh-Platform-Management", "Network": "ewh-Platform-Connectivity", "DataProtection": "ewh", "Monitoring": "ewh-Platform-Management", "Compliance": "ewh"}, "policyParameters": {"Security": {"effect": "<PERSON><PERSON>", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "Network": {"effect": "<PERSON><PERSON>", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "DataProtection": {"effect": "Audit", "enforcementMode": "<PERSON><PERSON><PERSON>"}, "Monitoring": {"effect": "DeployIfNotExists", "enforcementMode": "<PERSON><PERSON><PERSON>"}}, "workloadSpecificPolicies": {"apiManagement": {"managementGroups": ["ewh-ldz-prd", "ewh-ldz-non-prd"], "effect": "Audit"}, "storage": {"managementGroups": ["ewh-ldz-prd", "ewh-ldz-non-prd"], "effect": "<PERSON><PERSON>"}, "sql": {"managementGroups": ["ewh-ldz-prd"], "effect": "<PERSON><PERSON>"}, "kubernetes": {"managementGroups": ["ewh-ldz-prd", "ewh-ldz-non-prd"], "effect": "<PERSON><PERSON>"}}, "exemptions": {"sandboxExemptions": {"managementGroup": "ewh-Sandbox", "exemptPolicies": ["ALZ-Security-Baseline", "ALZ-Network-Security"], "reason": "Sandbox environment for testing"}}}