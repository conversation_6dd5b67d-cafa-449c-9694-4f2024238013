# Management Group Hierarchy Export - demo-ewh

## ⚠️ **VẤN ĐỀ VÀ GIẢI PHÁP QUAN TRỌNG**

### 🚨 **Vấn đề chính với ARM template gốc**
ARM template gốc (`mg-hierarchy-arm-template.json`) **KHÔNG THỂ DEPLOY ĐƯỢC** vì:
- **583 Policy Assignments** có `policyDefinitionId: null`
- Azure yêu cầu policy definition ID hợp lệ để tạo policy assignments
- What-If thành công nhưng deployment thực tế sẽ **FAIL**

### ✅ **Giải pháp đã tạo**

#### **1. Management Groups Only (KHUYẾN NGHỊ)**
- **Files**: `mg-hierarchy-only.json` + `mg-hierarchy-only.parameters.json`
- **Mô tả**: Chỉ tạo 11 Management Groups, không có policy assignments
- **Ưu điểm**: Deploy thành công 100%, tạo được cấu trúc hierarchy hoàn chỉnh
- **Script**: `Deploy-ManagementGroups-Only.ps1`

#### **2. Policy Fix Script (TÙY CHỌN)**
- **File**: `Fix-PolicyDefinitionIds.ps1`
- **Mô tả**: Cố gắng sửa policy definition IDs bằng placeholder values
- **Lưu ý**: Cần review và sửa manual các policy IDs

---

Thư mục này chứa kết quả export của Management Group hierarchy `demo-ewh` từ Azure và ARM template để tái tạo lại.

## 📁 Files được tạo

### ✅ **Working Solutions (KHUYẾN NGHỊ)**
- **`mg-hierarchy-only.json`** - ARM template chỉ Management Groups (DEPLOY ĐƯỢC)
- **`mg-hierarchy-only.parameters.json`** - Parameters file
- **`Deploy-ManagementGroups-Only.ps1`** - Script deployment thành công

### 📋 **Original Export (Reference Only)**
- **`mg-hierarchy-demo-ewh.json`** - Dữ liệu thô của hierarchy và policy assignments
- **`mg-hierarchy-arm-template.json`** - ARM template gốc (CÓ VẤN ĐỀ với policy IDs)
- **`mg-hierarchy-arm-template.parameters.json`** - Parameters file gốc
- **`Deploy-ManagementGroupHierarchy.ps1`** - Script deployment gốc (SẼ FAIL)

### 🔧 **Troubleshooting Tools**
- **`Fix-PolicyDefinitionIds.ps1`** - Script sửa policy definition IDs

## 🏗️ Cấu trúc Management Group đã export

```
demo-ewh (Root)
├── demo-ewh-landingzones
│   ├── demo-ewh-online (68 policies)
│   └── demo-ewh-corp (72 policies)
├── demo-ewh-decommissioned (18 policies)
├── demo-ewh-sandboxes (18 policies)
└── demo-ewh-platform (51 policies)
    ├── demo-ewh-connectivity (53 policies)
    ├── demo-ewh-management (52 policies + 1 subscription)
    ├── demo-ewh-identity (57 policies + 1 subscription)
    └── demo-ewh-security (51 policies + 1 subscription)
```

## 📊 Thống kê

- **11 Management Groups** tổng cộng
- **583 Policy Assignments** được áp dụng
- **3 Subscriptions** được đặt trong management groups
- **ARM Template**: 8,363 dòng code

## 🚀 Cách sử dụng (KHUYẾN NGHỊ)

### ✅ **Phương án 1: Deploy Management Groups Only (THÀNH CÔNG)**

#### **Preview trước khi deploy**
```powershell
.\Deploy-ManagementGroups-Only.ps1 -WhatIf
```

#### **Deploy thực tế**
```powershell
.\Deploy-ManagementGroups-Only.ps1
```

#### **Deploy với Azure CLI (What-If)**
```bash
az deployment tenant create --location "East US" --template-file mg-hierarchy-only.json --parameters @mg-hierarchy-only.parameters.json --what-if
```

#### **Deploy trực tiếp với Azure CLI**
```bash
az deployment tenant create --location "East US" --template-file mg-hierarchy-only.json --parameters @mg-hierarchy-only.parameters.json
```

### ⚠️ **Phương án 2: Sử dụng ARM template gốc (CÓ VẤN ĐỀ)**

#### **What-If (thành công)**
```powershell
.\Deploy-ManagementGroupHierarchy.ps1 -WhatIf
# ✅ What-If sẽ thành công
```

#### **Deploy thực tế (SẼ FAIL)**
```powershell
.\Deploy-ManagementGroupHierarchy.ps1
# ❌ Sẽ fail vì policy definition IDs = null
```

#### **Azure CLI (cũng sẽ fail)**
```bash
az deployment tenant create --location "East US" --template-file mg-hierarchy-arm-template.json --parameters @mg-hierarchy-arm-template.parameters.json
# ❌ Sẽ fail vì policy definition IDs = null
```

## 🎯 Các loại Policy được áp dụng

### Security Policies
- `Audit-ResourceRGLocation` - Kiểm tra vị trí resource groups
- `Deploy-ASC-Monitoring` - Deploy Azure Security Center monitoring
- `Enforce-ACSB` - Enforce Azure Cloud Security Benchmark
- `Deploy-MDFC-*` - Microsoft Defender for Cloud policies

### Network Policies
- `Deny-IP-forwarding` - Deny IP forwarding
- `Deny-MgmtPorts-Internet` - Deny management ports from internet
- `Deny-Subnet-Without-Nsg` - Require NSG on subnets
- `Enforce-AKS-HTTPS` - Enforce HTTPS on AKS

### Monitoring & Compliance
- `Deploy-VM-Monitoring` - Deploy VM monitoring
- `Deploy-AMBA-*` - Azure Monitor Baseline Alerts
- `Deploy-AzActivity-Log` - Deploy Activity Log settings
- `Deploy-VM-Backup` - Deploy VM backup

### Governance
- `Enforce-GR-*` - Guest Configuration policies
- `Audit-UnusedResources` - Audit unused resources
- `Deny-Classic-Resources` - Deny classic resources

## ⚠️ Lưu ý quan trọng

### Trước khi deploy
1. **Backup hiện tại**: Export cấu hình hiện tại trước khi thay đổi
2. **Test environment**: Deploy trên test environment trước
3. **Permissions**: Đảm bảo có quyền Management Group Contributor
4. **Dependencies**: Một số policies có thể cần policy definitions đã tồn tại

### Khi deploy
1. **What-If first**: Luôn chạy What-If trước khi deploy thực tế
2. **Incremental**: Có thể deploy từng phần thay vì toàn bộ
3. **Monitor**: Theo dõi deployment progress và errors

### Sau khi deploy
1. **Verify**: Kiểm tra tất cả management groups đã được tạo
2. **Policy compliance**: Kiểm tra policy compliance status
3. **Subscriptions**: Di chuyển subscriptions vào đúng management groups

## 🔧 Troubleshooting

### Lỗi thường gặp

#### "Management group already exists"
- ARM template sẽ update existing management groups
- Kiểm tra tên management groups có conflict không

#### "Policy definition not found"
- Một số policies cần policy definitions được deploy trước
- Kiểm tra policy definition IDs trong template

#### "Insufficient permissions"
- Cần quyền Management Group Contributor
- Cần quyền Policy Contributor cho policy assignments

#### "Circular dependency"
- Kiểm tra parent-child relationships
- Đảm bảo không có circular references

### Debug commands
```powershell
# Kiểm tra deployment status
Get-AzTenantDeployment -Name "mg-hierarchy-deployment-*"

# Kiểm tra management groups
Get-AzManagementGroup -GroupId "demo-ewh" -Expand -Recurse

# Kiểm tra policy assignments
Get-AzPolicyAssignment -Scope "/providers/Microsoft.Management/managementGroups/demo-ewh"
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra Azure PowerShell modules đã update
2. Verify Azure permissions
3. Check ARM template syntax
4. Review deployment logs trong Azure Portal

---

**Generated**: 2025-09-06 13:58:49  
**Source**: Management Group `demo-ewh`  
**Tenant**: 2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0
