#Requires -<PERSON><PERSON>les <PERSON>z.<PERSON>cco<PERSON>, Az.Resources

<#
.SYNOPSIS
    Retrieve missing policy definition IDs and update ARM template

.DESCRIPTION
    This script connects to Azure, retrieves all policy definition IDs based on display names,
    and updates the ARM template with the correct policy definition IDs.

.PARAMETER TemplateFile
    Path to the ARM template file to update

.PARAMETER OutputFile
    Path for the updated ARM template file

.EXAMPLE
    .\Get-PolicyDefinitionIds.ps1 -TemplateFile "mg-hierarchy-arm-template.json" -OutputFile "mg-hierarchy-fixed.json"
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$TemplateFile = ".\mg-hierarchy-arm-template.json",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputFile = ".\mg-hierarchy-arm-template-fixed.json"
)

Write-Host "Retrieving Policy Definition IDs from Azure" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Check if user is logged in to Azure
try {
    $context = Get-AzContext
    if (-not $context) {
        Write-Host "Please login to Azure first using Connect-AzAccount" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Current Azure Context:" -ForegroundColor Yellow
    Write-Host "  Subscription: $($context.Subscription.Name)" -ForegroundColor White
    Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White
    Write-Host ""
} catch {
    Write-Host "Error getting Azure context: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Load ARM template
if (-not (Test-Path $TemplateFile)) {
    Write-Host "Template file not found: $TemplateFile" -ForegroundColor Red
    exit 1
}

Write-Host "Loading ARM template: $TemplateFile" -ForegroundColor Yellow

try {
    $template = Get-Content $TemplateFile | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get policy assignments with null policy definition IDs
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }
$nullPolicyAssignments = $policyAssignments | Where-Object { 
    $null -eq $_.properties.policyDefinitionId -or 
    $_.properties.policyDefinitionId -eq "" -or
    $_.properties.policyDefinitionId -eq "null"
}

Write-Host "Found $($nullPolicyAssignments.Count) policy assignments with missing policy definition IDs" -ForegroundColor Yellow

# Get all policy definitions from Azure
Write-Host "Retrieving policy definitions from Azure..." -ForegroundColor Yellow

$builtInPolicies = @{}
$customPolicies = @{}

try {
    # Get built-in policy definitions
    Write-Host "  Getting built-in policy definitions..." -ForegroundColor Gray
    $builtInPolicyDefs = Get-AzPolicyDefinition | Where-Object { $_.Properties.PolicyType -eq "BuiltIn" }
    
    foreach ($policy in $builtInPolicyDefs) {
        $displayName = $policy.Properties.DisplayName
        if ($displayName -and -not $builtInPolicies.ContainsKey($displayName)) {
            $builtInPolicies[$displayName] = $policy.ResourceId
        }
    }
    
    Write-Host "    Found $($builtInPolicies.Count) built-in policies" -ForegroundColor Gray
    
    # Get custom policy definitions
    Write-Host "  Getting custom policy definitions..." -ForegroundColor Gray
    $customPolicyDefs = Get-AzPolicyDefinition | Where-Object { $_.Properties.PolicyType -eq "Custom" }
    
    foreach ($policy in $customPolicyDefs) {
        $displayName = $policy.Properties.DisplayName
        if ($displayName -and -not $customPolicies.ContainsKey($displayName)) {
            $customPolicies[$displayName] = $policy.ResourceId
        }
    }
    
    Write-Host "    Found $($customPolicies.Count) custom policies" -ForegroundColor Gray
    
} catch {
    Write-Host "Error retrieving policy definitions: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create combined policy lookup
$allPolicies = @{}
$builtInPolicies.GetEnumerator() | ForEach-Object { $allPolicies[$_.Key] = $_.Value }
$customPolicies.GetEnumerator() | ForEach-Object { $allPolicies[$_.Key] = $_.Value }

Write-Host "Total policy definitions available: $($allPolicies.Count)" -ForegroundColor Green

# Update policy assignments with correct policy definition IDs
$fixedCount = 0
$notFoundCount = 0
$notFoundPolicies = @()

Write-Host ""
Write-Host "Updating policy assignments..." -ForegroundColor Yellow

foreach ($assignment in $nullPolicyAssignments) {
    $displayName = $assignment.properties.displayName
    
    if ($allPolicies.ContainsKey($displayName)) {
        $assignment.properties.policyDefinitionId = $allPolicies[$displayName]
        $fixedCount++
        Write-Host "  ✅ Fixed: $displayName" -ForegroundColor Green
    } else {
        $notFoundCount++
        $notFoundPolicies += $displayName
        Write-Host "  ❌ Not found: $displayName" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Update Summary:" -ForegroundColor Yellow
Write-Host "  Total policy assignments: $($nullPolicyAssignments.Count)" -ForegroundColor White
Write-Host "  Successfully fixed: $fixedCount" -ForegroundColor Green
Write-Host "  Not found: $notFoundCount" -ForegroundColor Red

if ($notFoundCount -gt 0) {
    Write-Host ""
    Write-Host "Policies not found in Azure:" -ForegroundColor Red
    $notFoundPolicies | Sort-Object -Unique | ForEach-Object {
        Write-Host "  - $_" -ForegroundColor Red
    }
}

# Save updated template
Write-Host ""
Write-Host "Saving updated template to: $OutputFile" -ForegroundColor Yellow

try {
    $template | ConvertTo-Json -Depth 20 | Set-Content $OutputFile -Encoding UTF8
    Write-Host "✅ Template saved successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error saving template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create parameters file for the fixed template
$parametersFile = $OutputFile.Replace('.json', '.parameters.json')
$parametersContent = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = "demo-ewh"
        }
    }
}

$parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile -Encoding UTF8
Write-Host "✅ Parameters file saved: $parametersFile" -ForegroundColor Green

# Create deployment script for the fixed template
$deploymentScript = $OutputFile.Replace('.json', '-Deploy.ps1')
$scriptContent = @"
#Requires -Modules Az.Accounts, Az.Resources

param(
    [Parameter(Mandatory = `$false)]
    [switch]`$WhatIf
)

`$templateFile = "$($OutputFile | Split-Path -Leaf)"
`$parametersFile = "$($parametersFile | Split-Path -Leaf)"
`$deploymentName = "mg-hierarchy-fixed-`$(Get-Date -Format 'yyyyMMdd-HHmmss')"
`$location = "East US"

if (`$WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile
}
"@

$scriptContent | Set-Content $deploymentScript -Encoding UTF8
Write-Host "✅ Deployment script saved: $deploymentScript" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test with What-If: .\$($deploymentScript | Split-Path -Leaf) -WhatIf" -ForegroundColor White
Write-Host "2. Deploy: .\$($deploymentScript | Split-Path -Leaf)" -ForegroundColor White

if ($notFoundCount -gt 0) {
    Write-Host ""
    Write-Host "⚠️  Note: $notFoundCount policies were not found in Azure." -ForegroundColor Yellow
    Write-Host "   These may be custom policies that need to be created first," -ForegroundColor Yellow
    Write-Host "   or the display names may not match exactly." -ForegroundColor Yellow
}
