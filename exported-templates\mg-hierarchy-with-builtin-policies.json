{"outputs": null, "contentVersion": "1.0.0.0", "resources": [{"properties": {"displayName": "demo-ewh", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', '2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0')]"], "name": "demo-ewh", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-landingzones", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "demo-ewh-landingzones", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-online", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "demo-ewh-online", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-corp", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "demo-ewh-corp", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-decommissioned", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "demo-ewh-decommissioned", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-sandboxes", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "demo-ewh-sandboxes", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-platform", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "demo-ewh-platform", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-connectivity", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "demo-ewh-connectivity", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-management", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "demo-ewh-management", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-identity", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "demo-ewh-identity", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"displayName": "demo-ewh-security", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "demo-ewh-security", "apiVersion": "2021-04-01", "type": "Microsoft.Management/managementGroups"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "Audit-ResourceRGLocation-demo-ewh", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "Deny-Classic-Resources-demo-ewh", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "Audit-UnusedResources-demo-ewh", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "Audit-TrustedLaunch-demo-ewh", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh')]"], "name": "Deny-UnmanagedDisk-demo-ewh", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Audit-ResourceRGLocation-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-Classic-Resources-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Audit-UnusedResources-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Audit-TrustedLaunch-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-UnmanagedDisk-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "displayName": "Deny-IP-forwarding"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-IP-forwarding-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "displayName": "Deny-Subnet-Without-Nsg"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-Subnet-Without-Nsg-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "displayName": "Deny-MgmtPorts-Internet"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-MgmtPorts-Internet-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-landingzones", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "displayName": "Deny-Storage-http"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-landingzones')]"], "name": "Deny-Storage-http-demo-ewh-landingzones", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Audit-ResourceRGLocation-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-Classic-Resources-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Audit-UnusedResources-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Audit-TrustedLaunch-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-UnmanagedDisk-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "displayName": "Deny-IP-forwarding"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-IP-forwarding-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "displayName": "Deny-Subnet-Without-Nsg"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-Subnet-Without-Nsg-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "displayName": "Deny-MgmtPorts-Internet"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-MgmtPorts-Internet-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-online", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "displayName": "Deny-Storage-http"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-online')]"], "name": "Deny-Storage-http-demo-ewh-online", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Audit-ResourceRGLocation-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Deny-Classic-Resources-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Audit-UnusedResources-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Audit-TrustedLaunch-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Deny-UnmanagedDisk-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900", "displayName": "Deny-IP-forwarding"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Deny-IP-forwarding-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "displayName": "Deny-Subnet-Without-Nsg"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Deny-Subnet-Without-Nsg-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "displayName": "Deny-MgmtPorts-Internet"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "<PERSON>y-MgmtPorts-Internet-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-corp", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "displayName": "Deny-Storage-http"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-corp')]"], "name": "Deny-Storage-http-demo-ewh-corp", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-decommissioned", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-decommissioned')]"], "name": "Audit-ResourceRGLocation-demo-ewh-decommissioned", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-decommissioned", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-decommissioned')]"], "name": "Deny-Classic-Resources-demo-ewh-decommissioned", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-decommissioned", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-decommissioned')]"], "name": "Audit-UnusedResources-demo-ewh-decommissioned", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-decommissioned", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-decommissioned')]"], "name": "Audit-TrustedLaunch-demo-ewh-decommissioned", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-decommissioned", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-decommissioned')]"], "name": "Deny-UnmanagedDisk-demo-ewh-decommissioned", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-sandboxes", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-sandboxes')]"], "name": "Audit-ResourceRGLocation-demo-ewh-sandboxes", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-sandboxes", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-sandboxes')]"], "name": "Deny-Classic-Resources-demo-ewh-sandboxes", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-sandboxes", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-sandboxes')]"], "name": "Audit-UnusedResources-demo-ewh-sandboxes", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-sandboxes", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-sandboxes')]"], "name": "Audit-TrustedLaunch-demo-ewh-sandboxes", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-sandboxes", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-sandboxes')]"], "name": "Deny-UnmanagedDisk-demo-ewh-sandboxes", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "Audit-ResourceRGLocation-demo-ewh-platform", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "Deny-Classic-Resources-demo-ewh-platform", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "Audit-UnusedResources-demo-ewh-platform", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "Audit-TrustedLaunch-demo-ewh-platform", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-platform", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-platform')]"], "name": "Deny-UnmanagedDisk-demo-ewh-platform", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-connectivity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-connectivity')]"], "name": "Audit-ResourceRGLocation-demo-ewh-connectivity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-connectivity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-connectivity')]"], "name": "Deny-Classic-Resources-demo-ewh-connectivity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-connectivity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-connectivity')]"], "name": "Audit-UnusedResources-demo-ewh-connectivity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-connectivity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-connectivity')]"], "name": "Audit-TrustedLaunch-demo-ewh-connectivity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-connectivity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-connectivity')]"], "name": "Deny-UnmanagedDisk-demo-ewh-connectivity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-management", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-management')]"], "name": "Audit-ResourceRGLocation-demo-ewh-management", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-management", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-management')]"], "name": "Deny-Classic-Resources-demo-ewh-management", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-management", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-management')]"], "name": "Audit-UnusedResources-demo-ewh-management", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-management", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-management')]"], "name": "Audit-TrustedLaunch-demo-ewh-management", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-management", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-management')]"], "name": "Deny-UnmanagedDisk-demo-ewh-management", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Audit-ResourceRGLocation-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Deny-Classic-Resources-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Audit-UnusedResources-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Audit-TrustedLaunch-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Deny-UnmanagedDisk-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517", "displayName": "Deny-Subnet-Without-Nsg"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Deny-Subnet-Without-Nsg-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-identity", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "displayName": "Deny-MgmtPorts-Internet"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-identity')]"], "name": "Deny-MgmtPorts-Internet-demo-ewh-identity", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-security", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a", "displayName": "Audit-ResourceRGLocation"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-security')]"], "name": "Audit-ResourceRGLocation-demo-ewh-security", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-security", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25", "displayName": "Deny-Classic-Resources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-security')]"], "name": "Deny-Classic-Resources-demo-ewh-security", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-security", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e", "displayName": "Audit-UnusedResources"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-security')]"], "name": "Audit-UnusedResources-demo-ewh-security", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-security", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf", "displayName": "Audit-TrustedLaunch"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-security')]"], "name": "Audit-TrustedLaunch-demo-ewh-security", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}, {"properties": {"scope": "/providers/Microsoft.Management/managementGroups/demo-ewh-security", "enforcementMode": "<PERSON><PERSON><PERSON>", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d", "displayName": "Deny-UnmanagedDisk"}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', 'demo-ewh-security')]"], "name": "Deny-UnmanagedDisk-demo-ewh-security", "apiVersion": "2022-06-01", "type": "Microsoft.Authorization/policyAssignments"}], "$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "metadata": {"generatedDate": "2025-09-06 16:25:47", "description": "Management Group hierarchy for demo-ewh (with common built-in policies)", "successRate": "Partial (69 policies mapped)", "author": "Generated from existing Azure resources", "deploymentType": "Management Groups + Built-in Policies"}, "variables": {}, "parameters": {"enterpriseScaleCompanyPrefix": {"maxLength": 10, "defaultValue": "demo-ewh", "type": "string", "metadata": {"description": "Company prefix for Management Group hierarchy"}}}}