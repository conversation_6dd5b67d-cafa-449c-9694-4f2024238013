{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Management Groups Module", "version": "1.0.0"}, "description": "This template creates the Azure Landing Zone management group hierarchy"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "enableLite": {"type": "bool", "defaultValue": false, "metadata": {"description": "Enable Enterprise Scale Lite deployment mode (simplified management group structure)"}}}, "variables": {"mgmtGroups": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzPrdLegacy": "ldz-prd-legacy", "ldzPrdMicrosvc": "ldz-prd-microsvc", "ldzNonPrd": "ldz-non-prd", "ldzNonPrdUat": "ldz-non-prd-uat", "ldzNonPrdDev": "ldz-non-prd-dev", "sandboxes": "Sandbox", "decommissioned": "Decommissioned"}, "mgmtGroupsLite": {"eslzRoot": "[parameters('enterpriseScaleCompanyPrefix')]", "platform": "Platform", "management": "mg-Platform-Management", "connectivity": "mg-Platform-Connectivity", "lzs": "LandingZone", "ldzPrd": "ldz-prd", "ldzNonPrd": "ldz-non-prd", "sandboxes": "Sandbox", "decommissioned": "Decommissioned"}}, "resources": [{"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').eslzRoot]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'))]", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', tenant().tenantId)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').platform]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"], "properties": {"displayName": "Platform", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').management]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').platform)]"], "properties": {"displayName": "mg-Platform-Management ", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').platform)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').connectivity]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').platform)]"], "properties": {"displayName": "mg-Platform-Connectivity", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').platform)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').lzs]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"], "properties": {"displayName": "Landing Zone", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzPrd]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').lzs)]"], "properties": {"displayName": "ldz-prd", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').lzs)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzPrdLegacy]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzPrd)]"], "properties": {"displayName": "ldz-prd-legacy", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzPrd)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzPrdMicrosvc]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzPrd)]"], "properties": {"displayName": "ldz-prd-microsvc", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzPrd)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzNonPrd]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').lzs)]"], "properties": {"displayName": "ldz-non-prd", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').lzs)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzNonPrdUat]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzNonPrd)]"], "properties": {"displayName": "ldz-non-prd-uat", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzNonPrd)]"}}}}, {"condition": "[not(parameters('enableLite'))]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').ldzNonPrdDev]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzNonPrd)]"], "properties": {"displayName": "ldz-non-prd-dev", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').ldzNonPrd)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').sandboxes]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"], "properties": {"displayName": "Sandbox", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"}}}}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroups').decommissioned]", "dependsOn": ["[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"], "properties": {"displayName": "Decommissioned", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', variables('mgmtGroups').eslzRoot)]"}}}}, {"condition": "[parameters('enableLite')]", "type": "Microsoft.Management/managementGroups", "apiVersion": "2020-05-01", "name": "[variables('mgmtGroupsLite').eslzRoot]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), ' Root')]", "details": {"parent": {"id": "[tenantResourceId('Microsoft.Management/managementGroups/', tenant().tenantId)]"}}}}]}