{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/tenantDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"description": "Management Group hierarchy for demo-ewh (Management Groups only)", "author": "Generated from existing Azure resources", "generatedDate": "2025-09-06 15:20:00"}, "parameters": {"enterpriseScaleCompanyPrefix": {"type": "string", "defaultValue": "demo-ewh", "maxLength": 10, "metadata": {"description": "Company prefix for Management Group hierarchy"}}}, "variables": {}, "resources": [{"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[parameters('enterpriseScaleCompanyPrefix')]", "properties": {"displayName": "[parameters('enterpriseScaleCompanyPrefix')]", "details": {"parent": {"id": "/providers/Microsoft.Management/managementGroups/2c6ae73c-fd31-4e4a-88fc-24bcfcb2f5d0"}}}, "dependsOn": []}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'))]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-online')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-online')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-corp')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-corp')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-landingzones')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-decommissioned')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-decommissioned')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'))]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-sandboxes')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-sandboxes')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'))]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-platform')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-platform')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'))]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-connectivity')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-connectivity')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-platform')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-platform'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-management')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-management')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-platform')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-platform'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-identity')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-identity')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-platform')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-platform'))]"]}, {"type": "Microsoft.Management/managementGroups", "apiVersion": "2021-04-01", "name": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-security')]", "properties": {"displayName": "[concat(parameters('enterpriseScaleCompanyPrefix'), '-security')]", "details": {"parent": {"id": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('enterpriseScaleCompanyPrefix'), '-platform')]"}}}, "dependsOn": ["[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-platform'))]"]}], "outputs": {"managementGroupIds": {"type": "array", "value": ["[resourceId('Microsoft.Management/managementGroups', parameters('enterpriseScaleCompanyPrefix'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-landingzones'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-online'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-corp'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-decommissioned'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-sandboxes'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-platform'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-connectivity'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-management'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-identity'))]", "[resourceId('Microsoft.Management/managementGroups', concat(parameters('enterpriseScaleCompanyPrefix'), '-security'))]"]}}}