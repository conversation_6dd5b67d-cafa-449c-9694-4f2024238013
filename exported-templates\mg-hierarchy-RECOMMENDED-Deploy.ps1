﻿#Requires -Mo<PERSON>les <PERSON><PERSON>.Accounts, Az.Resources

param(
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

$templateFile = "mg-hierarchy-RECOMMENDED.json"
$parametersFile = "mg-hierarchy-RECOMMENDED.parameters.json"
$deploymentName = "mg-hierarchy-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$location = "East US"

Write-Host "Management Groups Only (RECOMMENDED)" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

if ($WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    $confirmation = Read-Host "Do you want to continue? (y/N)"
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile
    } else {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
    }
}
