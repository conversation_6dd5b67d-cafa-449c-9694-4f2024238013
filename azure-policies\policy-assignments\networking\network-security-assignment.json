{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Network Security Policy Assignment", "version": "1.0.0"}, "description": "This template assigns the Network Security initiative to management groups"}, "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "targetManagementGroupId": {"type": "string", "metadata": {"description": "The management group ID where the policy should be assigned"}}, "effect": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Effect for the policy assignment"}}, "enforcementMode": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "metadata": {"description": "Enforcement mode for the policy assignment"}}}, "variables": {"scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]", "assignmentScope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('targetManagementGroupId'))]", "policySetDefinitionId": "[concat(variables('scope'), '/providers/Microsoft.Authorization/policySetDefinitions/ALZ-Network-Security')]", "assignmentName": "ALZ-Network-Security", "assignmentDisplayName": "Azure Landing Zone Network Security Assignment", "assignmentDescription": "This assignment applies the Azure Landing Zone Network Security initiative to ensure network security compliance"}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2020-09-01", "name": "[variables('assignmentName')]", "properties": {"displayName": "[variables('assignmentDisplayName')]", "description": "[variables('assignmentDescription')]", "policyDefinitionId": "[variables('policySetDefinitionId')]", "scope": "[variables('assignmentScope')]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {}, "metadata": {"category": "Network", "source": "Azure Landing Zone", "assignedBy": "Azure Landing Zone Deployment"}}}], "outputs": {"policyAssignmentId": {"type": "string", "value": "[resourceId('Microsoft.Authorization/policyAssignments', variables('assignmentName'))]"}, "policyAssignmentName": {"type": "string", "value": "[variables('assignmentName')]"}, "assignmentScope": {"type": "string", "value": "[variables('assignmentScope')]"}}}