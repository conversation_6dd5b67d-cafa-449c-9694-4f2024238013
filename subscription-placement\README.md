# Subscription Placement Module

## 📋 Tổng quan

Module này cung cấp các công cụ để quản lý việc đặt subscription vào các Management Group trong Azure Landing Zone. Module được tách riêng từ template chính để dễ dàng quản lý và sử dụng độc lập.

## 🏗️ C<PERSON>u trúc <PERSON>

```
subscription-placement/
├── 📄 subscriptionOrganization.json          # ARM template đặt subscription
├── 📄 Deploy-SubscriptionPlacement.ps1       # Script deploy subscription placement
├── 📄 Move-Subscription.ps1                  # Script di chuyển subscription
├── 📄 Get-SubscriptionPlacements.ps1         # Script xem subscription placements
├── 📄 README.md                              # Tài liệu hướng dẫn
└── 📄 examples/                              # Thư mục ví dụ
    ├── bulk-placement-example.json
    └── subscription-mappings.csv
```

## 🚀 Cách sử dụng

### 1. Đặt một subscription vào Management Group

```powershell
.\Deploy-SubscriptionPlacement.ps1 `
    -SubscriptionId "12345678-1234-1234-1234-123456789012" `
    -TargetManagementGroupId "ewh-Platform-Management"
```

### 2. Đặt nhiều subscription cùng lúc (Bulk)

```powershell
$mappings = @(
    @{ SubscriptionId = "12345678-1234-1234-1234-123456789012"; TargetManagementGroupId = "ewh-Platform-Management" },
    @{ SubscriptionId = "87654321-4321-4321-4321-210987654321"; TargetManagementGroupId = "ewh-Platform-Connectivity" },
    @{ SubscriptionId = "11111111-2222-3333-4444-*********555"; TargetManagementGroupId = "ewh-ldz-prd-microsvc" }
)

.\Deploy-SubscriptionPlacement.ps1 -SubscriptionMappings $mappings
```

### 3. Di chuyển subscription giữa các Management Group

```powershell
.\Move-Subscription.ps1 `
    -SubscriptionId "12345678-1234-1234-1234-123456789012" `
    -SourceManagementGroupId "ewh-Platform-Management" `
    -TargetManagementGroupId "ewh-Platform-Connectivity"
```

### 4. Xem danh sách subscription placements

```powershell
# Xem tất cả
.\Get-SubscriptionPlacements.ps1

# Xem theo Management Group cụ thể
.\Get-SubscriptionPlacements.ps1 -ManagementGroupId "ewh-Platform-Management"

# Xem theo company prefix
.\Get-SubscriptionPlacements.ps1 -EnterpriseScaleCompanyPrefix "ewh"

# Xuất ra file CSV
.\Get-SubscriptionPlacements.ps1 -EnterpriseScaleCompanyPrefix "ewh" -ExportPath "C:\temp\subscriptions.csv"
```

### 5. Kiểm tra trước khi thực hiện (What-If)

```powershell
.\Deploy-SubscriptionPlacement.ps1 `
    -SubscriptionId "12345678-1234-1234-1234-123456789012" `
    -TargetManagementGroupId "ewh-Platform-Management" `
    -WhatIf
```

## 📊 Cấu trúc Management Group được hỗ trợ

Module này được thiết kế để hoạt động với cấu trúc Management Group tùy chỉnh:

```
ewh (Root)
├── Platform
│   ├── mg-Platform-Management      # Subscription cho tài nguyên quản lý chung
│   └── mg-Platform-Connectivity   # Subscription cho tài nguyên kết nối
├── LandingZone
│   ├── ldz-prd                    # Production environment
│   │   ├── ldz-prd-legacy         # Ứng dụng kiến trúc truyền thống
│   │   └── ldz-prd-microsvc       # Ứng dụng kiến trúc microservices
│   └── ldz-non-prd                # Non-production environment
│       ├── ldz-non-prd-uat        # UAT environment
│       └── ldz-non-prd-dev        # DEV environment
├── Sandbox                        # Sandbox environment
└── Decommissioned                 # Subscription dự kiến ngừng sử dụng
```

## 🔧 Tính năng chính

### ✅ Deploy-SubscriptionPlacement.ps1
- Đặt subscription vào Management Group
- Hỗ trợ single và bulk operations
- What-If mode để kiểm tra trước
- Validation và error handling
- Logging chi tiết

### ✅ Move-Subscription.ps1
- Di chuyển subscription giữa các Management Group
- Validation source và target Management Group
- Rollback tự động khi có lỗi
- Confirmation prompts (có thể skip với -Force)
- What-If mode

### ✅ Get-SubscriptionPlacements.ps1
- Hiển thị subscription placements theo hierarchy
- Filter theo Management Group hoặc company prefix
- Export ra CSV
- Hiển thị subscription state (Enabled/Disabled/Warned)
- Summary statistics

### ✅ subscriptionOrganization.json
- ARM template chuẩn cho subscription placement
- Sử dụng resource type: `Microsoft.Management/managementGroups/subscriptions`
- Output parameters để integration
- Metadata và documentation

## 📝 Ví dụ thực tế

### Scenario 1: Setup Platform Subscriptions

```powershell
# Đặt Management subscription
.\Deploy-SubscriptionPlacement.ps1 `
    -SubscriptionId "mgmt-sub-12345678-1234-1234-1234-123456789012" `
    -TargetManagementGroupId "ewh-Platform-Management"

# Đặt Connectivity subscription  
.\Deploy-SubscriptionPlacement.ps1 `
    -SubscriptionId "conn-sub-87654321-4321-4321-4321-210987654321" `
    -TargetManagementGroupId "ewh-Platform-Connectivity"
```

### Scenario 2: Organize Application Subscriptions

```powershell
$appSubscriptions = @(
    @{ SubscriptionId = "app1-prd-11111111-2222-3333-4444-*********555"; TargetManagementGroupId = "ewh-ldz-prd-microsvc" },
    @{ SubscriptionId = "app1-uat-22222222-3333-4444-5555-666666666666"; TargetManagementGroupId = "ewh-ldz-non-prd-uat" },
    @{ SubscriptionId = "app1-dev-33333333-4444-5555-6666-777777777777"; TargetManagementGroupId = "ewh-ldz-non-prd-dev" },
    @{ SubscriptionId = "legacy-app-44444444-5555-6666-7777-888888888888"; TargetManagementGroupId = "ewh-ldz-prd-legacy" }
)

.\Deploy-SubscriptionPlacement.ps1 -SubscriptionMappings $appSubscriptions
```

### Scenario 3: Migration từ Management Group cũ

```powershell
# Di chuyển subscription từ cấu trúc cũ sang mới
.\Move-Subscription.ps1 `
    -SubscriptionId "12345678-1234-1234-1234-123456789012" `
    -SourceManagementGroupId "old-corp" `
    -TargetManagementGroupId "ewh-ldz-prd-microsvc" `
    -Force
```

## ⚠️ Lưu ý quan trọng

1. **Permissions**: Cần quyền `Management Group Contributor` hoặc `Owner` ở tenant root level
2. **Subscription State**: Chỉ có thể di chuyển subscription ở trạng thái `Enabled`
3. **Policy Impact**: Di chuyển subscription có thể ảnh hưởng đến policy assignments
4. **Billing**: Kiểm tra billing scope sau khi di chuyển subscription
5. **Dependencies**: Đảm bảo không có dependencies giữa resources khi di chuyển

## 🔍 Troubleshooting

### Lỗi thường gặp

**1. "Management Group not found"**
```powershell
# Kiểm tra Management Group tồn tại
Get-AzManagementGroup -GroupId "ewh-Platform-Management"
```

**2. "Insufficient permissions"**
```powershell
# Kiểm tra quyền hiện tại
Get-AzRoleAssignment -Scope "/providers/Microsoft.Management/managementGroups/ewh"
```

**3. "Subscription not found"**
```powershell
# Kiểm tra subscription access
Get-AzSubscription -SubscriptionId "12345678-1234-1234-1234-123456789012"
```

## 🔗 Tích hợp với Management Groups Module

Module này được thiết kế để hoạt động cùng với Management Groups module:

```powershell
# 1. Tạo Management Group hierarchy
cd ..\management-groups
.\Deploy-ManagementGroups.ps1 -EnterpriseScaleCompanyPrefix "ewh" -Location "East Asia"

# 2. Đặt subscriptions vào các Management Group
cd ..\subscription-placement
.\Deploy-SubscriptionPlacement.ps1 -SubscriptionMappings $mappings

# 3. Kiểm tra kết quả
.\Get-SubscriptionPlacements.ps1 -EnterpriseScaleCompanyPrefix "ewh"
```

## 📚 Tài liệu tham khảo

- [Azure Management Groups Documentation](https://docs.microsoft.com/en-us/azure/governance/management-groups/)
- [ARM Template Reference - Management Groups](https://docs.microsoft.com/en-us/azure/templates/microsoft.management/managementgroups)
- [Azure PowerShell - Management Groups](https://docs.microsoft.com/en-us/powershell/module/az.resources/)
