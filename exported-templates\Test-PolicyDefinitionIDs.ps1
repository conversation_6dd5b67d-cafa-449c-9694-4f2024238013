#Requires -<PERSON><PERSON><PERSON><PERSON>, Az.Resources

<#
.SYNOPSIS
    Test policy definition IDs in the ARM template to identify issues

.DESCRIPTION
    This script checks the ARM template for policy assignments with null or invalid policy definition IDs
    and provides a detailed report of the issues.

.EXAMPLE
    .\Test-PolicyDefinitionIDs.ps1
#>

param()

Write-Host "Testing Policy Definition IDs in ARM Template" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$templateFile = ".\mg-hierarchy-arm-template.json"

if (-not (Test-Path $templateFile)) {
    Write-Host "Template file not found: $templateFile" -ForegroundColor Red
    exit 1
}

Write-Host "Loading ARM template..." -ForegroundColor Yellow

try {
    $template = Get-Content $templateFile | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Template loaded successfully!" -ForegroundColor Green
Write-Host "Total resources in template: $($template.resources.Count)" -ForegroundColor White

# Analyze policy assignments
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }
$managementGroups = $template.resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }

Write-Host ""
Write-Host "Resource Analysis:" -ForegroundColor Yellow
Write-Host "  Management Groups: $($managementGroups.Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White

# Check for null policy definition IDs
$nullPolicyIds = $policyAssignments | Where-Object { 
    $null -eq $_.properties.policyDefinitionId -or 
    $_.properties.policyDefinitionId -eq "" -or
    $_.properties.policyDefinitionId -eq "null"
}

Write-Host ""
Write-Host "Policy Definition ID Analysis:" -ForegroundColor Yellow
Write-Host "  Total Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White
Write-Host "  Assignments with NULL Policy Definition ID: $($nullPolicyIds.Count)" -ForegroundColor Red
Write-Host "  Assignments with valid Policy Definition ID: $($policyAssignments.Count - $nullPolicyIds.Count)" -ForegroundColor Green

if ($nullPolicyIds.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ CRITICAL ISSUE FOUND!" -ForegroundColor Red
    Write-Host "The following policy assignments have NULL policy definition IDs:" -ForegroundColor Red
    
    $nullPolicyIds | Select-Object -First 10 | ForEach-Object {
        Write-Host "  - $($_.name) (Display: $($_.properties.displayName))" -ForegroundColor Red
    }
    
    if ($nullPolicyIds.Count -gt 10) {
        Write-Host "  ... and $($nullPolicyIds.Count - 10) more" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "🚨 DEPLOYMENT WILL FAIL!" -ForegroundColor Red
    Write-Host "Azure requires valid policy definition IDs for all policy assignments." -ForegroundColor Red
    Write-Host ""
    Write-Host "RECOMMENDED SOLUTIONS:" -ForegroundColor Yellow
    Write-Host "1. Use 'mg-hierarchy-only.json' to deploy Management Groups only" -ForegroundColor Green
    Write-Host "2. Run 'Fix-PolicyDefinitionIds.ps1' to attempt fixing policy IDs" -ForegroundColor Yellow
    Write-Host "3. Deploy policies separately after Management Groups are created" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "✅ All policy assignments have valid policy definition IDs!" -ForegroundColor Green
    Write-Host "Template should deploy successfully." -ForegroundColor Green
}

# Check for unique policy assignment names
$duplicateNames = $policyAssignments | Group-Object name | Where-Object { $_.Count -gt 1 }

if ($duplicateNames.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️ WARNING: Duplicate policy assignment names found!" -ForegroundColor Yellow
    $duplicateNames | ForEach-Object {
        Write-Host "  - $($_.Name) appears $($_.Count) times" -ForegroundColor Yellow
    }
} else {
    Write-Host ""
    Write-Host "✅ All policy assignment names are unique!" -ForegroundColor Green
}

# Sample of policy assignments with issues
if ($nullPolicyIds.Count -gt 0) {
    Write-Host ""
    Write-Host "Sample of problematic policy assignments:" -ForegroundColor Yellow
    
    $sample = $nullPolicyIds | Select-Object -First 5
    foreach ($policy in $sample) {
        Write-Host ""
        Write-Host "Policy: $($policy.name)" -ForegroundColor Red
        Write-Host "  Display Name: $($policy.properties.displayName)" -ForegroundColor White
        Write-Host "  Scope: $($policy.properties.scope)" -ForegroundColor White
        Write-Host "  Policy Definition ID: $($policy.properties.policyDefinitionId)" -ForegroundColor Red
        Write-Host "  Enforcement Mode: $($policy.properties.enforcementMode)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Analysis completed!" -ForegroundColor Green
