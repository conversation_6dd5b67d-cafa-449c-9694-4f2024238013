﻿#Requires -Mo<PERSON>les <PERSON>z.Accounts, Az.Resources

param(
    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

$templateFile = "mg-hierarchy-with-policies-fixed.json"
$parametersFile = "mg-hierarchy-with-policies-fixed.parameters.json"
$deploymentName = "mg-hierarchy-with-policies-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$location = "East US"

Write-Host "Deploying Management Group Hierarchy with Policies" -ForegroundColor Green
Write-Host "==================================================" -ForegroundColor Green

if ($WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    $confirmation = Read-Host "Do you want to continue? (y/N)"
    if ($confirmation -eq 'y' -or $confirmation -eq 'Y') {
        New-AzTenantDeployment -Name $deploymentName -Location $location -TemplateFile $templateFile -TemplateParameterFile $parametersFile
    } else {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
    }
}
