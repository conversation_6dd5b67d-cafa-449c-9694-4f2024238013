# Complete Management Group Export Script
param(
    [string]$ManagementGroupPrefix = "demo-ewh",
    [string]$OutputPath = ".\exported-templates"
)

# Create output directory
if (-not (Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "Created output directory: $OutputPath" -ForegroundColor Green
}

Write-Host "Management Group Export for: $ManagementGroupPrefix" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Import modules
Import-Module Az.Accounts -Force
Import-Module Az.Resources -Force

# Check context
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Context: $($context.Subscription.Name)" -ForegroundColor Green

# Function to get management group details recursively
function Get-MgDetails {
    param(
        [string]$MgId,
        [int]$Level = 0
    )
    
    $indent = "  " * $Level
    
    try {
        $mg = Get-AzManagementGroup -GroupId $MgId -Expand -Recurse
        Write-Host "$indent+ $($mg.DisplayName) ($($mg.Name))" -ForegroundColor Yellow
        
        $mgInfo = @{
            Id = $mg.Name
            DisplayName = $mg.DisplayName
            ParentId = $mg.ParentId
            Children = @()
            Subscriptions = @()
            PolicyAssignments = @()
            Level = $Level
        }
        
        # Get policy assignments for this MG
        try {
            $scope = "/providers/Microsoft.Management/managementGroups/$($mg.Name)"
            $assignments = Get-AzPolicyAssignment -Scope $scope
            
            foreach ($assignment in $assignments) {
                $policyName = if ($assignment.Properties.DisplayName) { 
                    $assignment.Properties.DisplayName 
                } else { 
                    $assignment.Name 
                }
                
                Write-Host "$indent  Policy: $policyName" -ForegroundColor Cyan
                
                $mgInfo.PolicyAssignments += @{
                    Name = $assignment.Name
                    DisplayName = $assignment.Properties.DisplayName
                    PolicyDefinitionId = $assignment.Properties.PolicyDefinitionId
                    Scope = $assignment.Properties.Scope
                    Parameters = $assignment.Properties.Parameters
                    EnforcementMode = $assignment.Properties.EnforcementMode
                }
            }
        }
        catch {
            Write-Warning "$indent  Could not get policies: $($_.Exception.Message)"
        }
        
        # Process children
        if ($mg.Children) {
            foreach ($child in $mg.Children) {
                if ($child.Type -eq "/subscriptions") {
                    Write-Host "$indent  Subscription: $($child.DisplayName) ($($child.Name))" -ForegroundColor Green
                    $mgInfo.Subscriptions += @{
                        Id = $child.Name
                        DisplayName = $child.DisplayName
                    }
                }
                elseif ($child.Type -eq "Microsoft.Management/managementGroups") {
                    $childInfo = Get-MgDetails -MgId $child.Name -Level ($Level + 1)
                    if ($childInfo) {
                        $mgInfo.Children += $childInfo
                    }
                }
            }
        }
        
        return $mgInfo
    }
    catch {
        Write-Warning "$indent  Error getting MG $MgId : $($_.Exception.Message)"
        return $null
    }
}

# Get the complete hierarchy
Write-Host "`nScanning Management Group Hierarchy..." -ForegroundColor Cyan
$hierarchy = Get-MgDetails -MgId $ManagementGroupPrefix

if (-not $hierarchy) {
    Write-Error "Could not retrieve management group: $ManagementGroupPrefix"
    exit 1
}

# Export to JSON
$exportData = @{
    ManagementGroupHierarchy = $hierarchy
    ExportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    TenantId = $context.Tenant.Id
    SubscriptionId = $context.Subscription.Id
    ExportedBy = $context.Account.Id
}

$jsonPath = Join-Path $OutputPath "mg-hierarchy-$ManagementGroupPrefix.json"
$exportData | ConvertTo-Json -Depth 15 | Out-File -FilePath $jsonPath -Encoding UTF8

Write-Host "`nExport Summary:" -ForegroundColor Green
Write-Host "JSON exported to: $jsonPath" -ForegroundColor White

# Count resources
function Count-Resources {
    param($MgInfo)
    
    $mgCount = 1
    $policyCount = $MgInfo.PolicyAssignments.Count
    $subCount = $MgInfo.Subscriptions.Count
    
    foreach ($child in $MgInfo.Children) {
        $childCounts = Count-Resources -MgInfo $child
        $mgCount += $childCounts.ManagementGroups
        $policyCount += $childCounts.Policies
        $subCount += $childCounts.Subscriptions
    }
    
    return @{
        ManagementGroups = $mgCount
        Policies = $policyCount
        Subscriptions = $subCount
    }
}

$counts = Count-Resources -MgInfo $hierarchy
Write-Host "Total Management Groups: $($counts.ManagementGroups)" -ForegroundColor White
Write-Host "Total Policy Assignments: $($counts.Policies)" -ForegroundColor White
Write-Host "Total Subscriptions: $($counts.Subscriptions)" -ForegroundColor White

Write-Host "`nNext: Run Convert-ToARMTemplate.ps1 to create ARM template" -ForegroundColor Yellow
Write-Host "Export completed successfully!" -ForegroundColor Green
