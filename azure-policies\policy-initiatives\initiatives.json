{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Azure Policy Initiatives Module", "version": "1.0.0"}, "description": "This template deploys Azure Policy initiatives (sets) for Azure Landing Zone"}, "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}}, "variables": {"scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]", "policySetDefinitions": {"securityBaseline": {"name": "ALZ-Security-Baseline", "displayName": "Azure Landing Zone Security Baseline", "description": "This initiative includes security policies for Azure Landing Zone"}, "networkSecurity": {"name": "ALZ-Network-Security", "displayName": "Azure Landing Zone Network Security", "description": "This initiative includes network security policies for Azure Landing Zone"}, "dataProtection": {"name": "ALZ-Data-Protection", "displayName": "Azure Landing Zone Data Protection", "description": "This initiative includes data protection policies for Azure Landing Zone"}}, "customPolicyDefinitions": {"denyClassicResources": "[concat(variables('scope'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-Classic-Resources')]", "requireNsgOnSubnet": "[concat(variables('scope'), '/providers/Microsoft.Authorization/policyDefinitions/Require-NSG-On-Subnet')]", "denyMgmtPortsFromInternet": "[concat(variables('scope'), '/providers/Microsoft.Authorization/policyDefinitions/Deny-Mgmt-Ports-From-Internet')]", "requireStorageHttps": "[concat(variables('scope'), '/providers/Microsoft.Authorization/policyDefinitions/Require-Storage-Https')]"}}, "resources": [{"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policySetDefinitions').securityBaseline.name]", "dependsOn": [], "properties": {"displayName": "[variables('policySetDefinitions').securityBaseline.displayName]", "description": "[variables('policySetDefinitions').securityBaseline.description]", "policyType": "Custom", "metadata": {"category": "Security", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policies"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyDefinitions": [{"policyDefinitionId": "[variables('customPolicyDefinitions').denyClassicResources]", "parameters": {}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/34c877ad-507e-4c82-993e-3452a6e0ad3c", "parameters": {"effect": {"value": "[parameters('effect')]"}}}]}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policySetDefinitions').networkSecurity.name]", "dependsOn": [], "properties": {"displayName": "[variables('policySetDefinitions').networkSecurity.displayName]", "description": "[variables('policySetDefinitions').networkSecurity.description]", "policyType": "Custom", "metadata": {"category": "Network", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policies"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyDefinitions": [{"policyDefinitionId": "[variables('customPolicyDefinitions').requireNsgOnSubnet]", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "[variables('customPolicyDefinitions').denyMgmtPortsFromInternet]", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/6b1cbf55-e8b6-442f-ba4c-7246b6381474", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/83a86a26-fd1f-447c-b59d-e51f44264114", "parameters": {"effect": {"value": "[parameters('effect')]"}}}]}}, {"type": "Microsoft.Authorization/policySetDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policySetDefinitions').dataProtection.name]", "dependsOn": [], "properties": {"displayName": "[variables('policySetDefinitions').dataProtection.displayName]", "description": "[variables('policySetDefinitions').dataProtection.description]", "policyType": "Custom", "metadata": {"category": "Data Protection", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policies"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyDefinitions": [{"policyDefinitionId": "[variables('customPolicyDefinitions').requireStorageHttps]", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/17k78e20-9358-41c9-923c-fb736d382a12", "parameters": {"effect": {"value": "[parameters('effect')]"}}}, {"policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/86efb160-8de7-451d-bc08-5d475b0aadae", "parameters": {"effect": {"value": "[parameters('effect')]"}}}]}}], "outputs": {"policySetDefinitionIds": {"type": "object", "value": {"securityBaseline": "[resourceId('Microsoft.Authorization/policySetDefinitions', variables('policySetDefinitions').securityBaseline.name)]", "networkSecurity": "[resourceId('Microsoft.Authorization/policySetDefinitions', variables('policySetDefinitions').networkSecurity.name)]", "dataProtection": "[resourceId('Microsoft.Authorization/policySetDefinitions', variables('policySetDefinitions').dataProtection.name)]"}}}}