# Deploy Azure Policy Definitions Module
# This script deploys custom Azure Policy definitions for Azure Landing Zone

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [string]$TemplateFile,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Import required modules
$requiredModules = @('Az.Accounts', 'Az.Resources')
foreach ($module in $requiredModules) {
    if (-not (Get-Module -Name $module -ListAvailable)) {
        Write-Host "Installing module: $module" -ForegroundColor Yellow
        Install-Module -Name $module -Force -AllowClobber
    }
    Import-Module -Name $module -Force
}

# Check if user is logged in
$context = Get-AzContext
if (-not $context) {
    Write-Host "Please log in to Azure..." -ForegroundColor Yellow
    Connect-AzAccount
    $context = Get-AzContext
}

Write-Host "Current Azure Context:" -ForegroundColor Green
Write-Host "  Subscription: $($context.Subscription.Name) ($($context.Subscription.Id))" -ForegroundColor White
Write-Host "  Tenant: $($context.Tenant.Id)" -ForegroundColor White

# Set default template file path if not provided
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
if (-not $TemplateFile) {
    $TemplateFile = Join-Path $scriptPath "policies.json"
}

# Validate template file exists
if (-not (Test-Path $TemplateFile)) {
    Write-Error "Template file not found: $TemplateFile"
    exit 1
}

# Generate deployment name
$deploymentName = "alz-policy-definitions-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# Prepare deployment parameters
$deploymentParameters = @{
    Name                = $deploymentName
    ManagementGroupId   = $EnterpriseScaleCompanyPrefix
    TemplateFile        = $TemplateFile
    topLevelManagementGroupPrefix = $EnterpriseScaleCompanyPrefix
}

Write-Host "`nPolicy Definitions Deployment Configuration:" -ForegroundColor Green
Write-Host "  Management Group: $EnterpriseScaleCompanyPrefix" -ForegroundColor White
Write-Host "  Template File: $TemplateFile" -ForegroundColor White
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor White

try {
    if ($WhatIf) {
        Write-Host "`nRunning What-If analysis..." -ForegroundColor Yellow
        $result = New-AzManagementGroupDeployment @deploymentParameters -WhatIf
        Write-Host "What-If analysis completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "`nDeploying policy definitions..." -ForegroundColor Yellow
        $result = New-AzManagementGroupDeployment @deploymentParameters
        
        if ($result.ProvisioningState -eq "Succeeded") {
            Write-Host "✅ Policy definitions deployed successfully!" -ForegroundColor Green
        }
        else {
            Write-Error "Deployment failed with state: $($result.ProvisioningState)"
        }
    }
}
catch {
    Write-Error "Failed to deploy policy definitions: $($_.Exception.Message)"
    throw
}

Write-Host "`nPolicy definitions deployment completed!" -ForegroundColor Green

# Verify deployment
Write-Host "`n🔍 Verifying policy definitions..." -ForegroundColor Cyan
try {
    $policies = Get-AzPolicyDefinition -ManagementGroupName $EnterpriseScaleCompanyPrefix | Where-Object { $_.Properties.PolicyType -eq "Custom" }
    Write-Host "Found $($policies.Count) custom policy definition(s) in management group '$EnterpriseScaleCompanyPrefix'" -ForegroundColor Green
    
    foreach ($policy in $policies) {
        Write-Host "  • $($policy.Name): $($policy.Properties.DisplayName)" -ForegroundColor White
    }
}
catch {
    Write-Warning "Could not verify policy definitions: $($_.Exception.Message)"
}

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Deploy policy initiatives: cd ../policy-initiatives" -ForegroundColor White
Write-Host "2. Run: .\Deploy-PolicyInitiatives.ps1 -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix" -ForegroundColor White
