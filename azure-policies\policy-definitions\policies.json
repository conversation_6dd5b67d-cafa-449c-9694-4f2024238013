{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Azure Policy Definitions Module", "version": "1.0.0"}, "description": "This template deploys custom Azure Policy definitions for Azure Landing Zone"}, "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}}, "variables": {"scope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'))]", "policyDefinitions": {"denyClassicResources": {"name": "Deny-Classic-Resources", "displayName": "Deny deployment of classic resources", "description": "This policy denies deployment of classic compute, network and storage resources"}, "denyPublicEndpoints": {"name": "Deny-Public-Endpoints", "displayName": "Deny public endpoints for PaaS services", "description": "This policy denies creation of public endpoints for PaaS services"}, "requireNsgOnSubnet": {"name": "Require-NSG-On-Subnet", "displayName": "Require Network Security Group on subnets", "description": "This policy requires a Network Security Group to be associated with subnets"}, "denyMgmtPortsFromInternet": {"name": "Deny-Mgmt-Ports-From-Internet", "displayName": "Deny management ports from Internet", "description": "This policy denies management ports (RDP, SSH, WinRM) from Internet"}, "requireStorageHttps": {"name": "Require-Storage-Https", "displayName": "Require HTTPS for storage accounts", "description": "This policy requires HTTPS for storage account access"}}}, "resources": [{"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policyDefinitions').denyClassicResources.name]", "properties": {"displayName": "[variables('policyDefinitions').denyClassicResources.displayName]", "description": "[variables('policyDefinitions').denyClassicResources.description]", "policyType": "Custom", "mode": "All", "metadata": {"category": "General", "source": "Azure Landing Zone"}, "policyRule": {"if": {"anyOf": [{"field": "type", "equals": "Microsoft.ClassicCompute/virtualMachines"}, {"field": "type", "equals": "Microsoft.ClassicNetwork/virtualNetworks"}, {"field": "type", "equals": "Microsoft.ClassicStorage/storageAccounts"}]}, "then": {"effect": "deny"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policyDefinitions').requireNsgOnSubnet.name]", "properties": {"displayName": "[variables('policyDefinitions').requireNsgOnSubnet.displayName]", "description": "[variables('policyDefinitions').requireNsgOnSubnet.description]", "policyType": "Custom", "mode": "All", "metadata": {"category": "Network", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/virtualNetworks/subnets"}, {"field": "Microsoft.Network/virtualNetworks/subnets/networkSecurityGroup.id", "exists": "false"}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policyDefinitions').denyMgmtPortsFromInternet.name]", "properties": {"displayName": "[variables('policyDefinitions').denyMgmtPortsFromInternet.displayName]", "description": "[variables('policyDefinitions').denyMgmtPortsFromInternet.description]", "policyType": "Custom", "mode": "All", "metadata": {"category": "Network", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkSecurityGroups/securityRules"}, {"allOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/access", "equals": "Allow"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/direction", "equals": "Inbound"}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange", "in": ["22", "3389", "5985", "5986"]}, {"value": "[if(and(not(empty(field('Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange'))), contains(createArray('22', '3389', '5985', '5986'), field('Microsoft.Network/networkSecurityGroups/securityRules/destinationPortRange'))), 'true', 'false')]", "equals": "true"}]}, {"anyOf": [{"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "*"}, {"field": "Microsoft.Network/networkSecurityGroups/securityRules/sourceAddressPrefix", "equals": "Internet"}]}]}]}, "then": {"effect": "[parameters('effect')]"}}}}, {"type": "Microsoft.Authorization/policyDefinitions", "apiVersion": "2020-09-01", "name": "[variables('policyDefinitions').requireStorageHttps.name]", "properties": {"displayName": "[variables('policyDefinitions').requireStorageHttps.displayName]", "description": "[variables('policyDefinitions').requireStorageHttps.description]", "policyType": "Custom", "mode": "All", "metadata": {"category": "Storage", "source": "Azure Landing Zone"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "Enable or disable the execution of the policy"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Storage/storageAccounts"}, {"field": "Microsoft.Storage/storageAccounts/supportsHttpsTrafficOnly", "notEquals": "true"}]}, "then": {"effect": "[parameters('effect')]"}}}}], "outputs": {"policyDefinitionIds": {"type": "object", "value": {"denyClassicResources": "[resourceId('Microsoft.Authorization/policyDefinitions', variables('policyDefinitions').denyClassicResources.name)]", "requireNsgOnSubnet": "[resourceId('Microsoft.Authorization/policyDefinitions', variables('policyDefinitions').requireNsgOnSubnet.name)]", "denyMgmtPortsFromInternet": "[resourceId('Microsoft.Authorization/policyDefinitions', variables('policyDefinitions').denyMgmtPortsFromInternet.name)]", "requireStorageHttps": "[resourceId('Microsoft.Authorization/policyDefinitions', variables('policyDefinitions').requireStorageHttps.name)]"}}}}