{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "ALZ Storage Security Policy Assignment", "version": "1.0.0"}, "description": "This template assigns storage security policies to management groups"}, "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "maxLength": 10, "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "targetManagementGroupId": {"type": "string", "metadata": {"description": "The management group ID where the policy should be assigned"}}, "effect": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Effect for the policy assignment"}}, "enforcementMode": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "metadata": {"description": "Enforcement mode for the policy assignment"}}}, "variables": {"assignmentScope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('targetManagementGroupId'))]", "assignments": [{"name": "ALZ-Storage-HTTPS-Assignment", "displayName": "Require HTTPS for Storage Accounts", "description": "This assignment requires HTTPS for storage account access", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9"}, {"name": "ALZ-Storage-PublicAccess-Assignment", "displayName": "Deny Storage Account Public Access", "description": "This assignment denies public access to storage accounts", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/4fa4b6c0-31ca-4c0d-b10d-24b96f62a751"}, {"name": "ALZ-Storage-MinTLS-Assignment", "displayName": "Require Minimum TLS Version for Storage", "description": "This assignment requires minimum TLS version for storage accounts", "policyDefinitionId": "/providers/Microsoft.Authorization/policyDefinitions/fe83a0eb-a853-422d-aac2-1bffd182c5d0"}]}, "resources": [{"copy": {"name": "storageSecurityAssignments", "count": "[length(variables('assignments'))]"}, "type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2020-09-01", "name": "[variables('assignments')[copyIndex()].name]", "properties": {"displayName": "[variables('assignments')[copyIndex()].displayName]", "description": "[variables('assignments')[copyIndex()].description]", "policyDefinitionId": "[variables('assignments')[copyIndex()].policyDefinitionId]", "scope": "[variables('assignmentScope')]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {"effect": {"value": "[parameters('effect')]"}}, "metadata": {"category": "Storage", "source": "Azure Landing Zone", "assignedBy": "Azure Landing Zone Deployment"}}}], "outputs": {"policyAssignmentIds": {"type": "array", "copy": {"count": "[length(variables('assignments'))]", "input": {"name": "[variables('assignments')[copyIndex()].name]", "id": "[resourceId('Microsoft.Authorization/policyAssignments', variables('assignments')[copyIndex()].name)]"}}}}}