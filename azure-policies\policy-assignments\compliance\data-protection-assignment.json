{"$schema": "https://schema.management.azure.com/schemas/2019-08-01/managementGroupDeploymentTemplate.json#", "contentVersion": "*******", "parameters": {"topLevelManagementGroupPrefix": {"type": "string", "metadata": {"description": "Provide a prefix (max 10 characters, unique at tenant-scope) for the Management Group hierarchy."}}, "targetManagementGroupId": {"type": "string", "metadata": {"description": "Target Management Group ID for policy assignment."}}, "effect": {"type": "string", "defaultValue": "<PERSON><PERSON>", "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "metadata": {"description": "Effect for the policy assignment."}}, "enforcementMode": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>", "allowedValues": ["<PERSON><PERSON><PERSON>", "DoNotEnforce"], "metadata": {"description": "Enforcement mode for the policy assignment."}}}, "variables": {"policyAssignmentName": "ALZ-DataProtection", "policySetDefinitionId": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('topLevelManagementGroupPrefix'), '/providers/Microsoft.Authorization/policySetDefinitions/ALZ-Data-Protection')]", "assignmentScope": "[concat('/providers/Microsoft.Management/managementGroups/', parameters('targetManagementGroupId'))]"}, "resources": [{"type": "Microsoft.Authorization/policyAssignments", "apiVersion": "2022-06-01", "name": "[variables('policyAssignmentName')]", "properties": {"displayName": "Azure Landing Zone - Data Protection Initiative", "description": "This initiative includes policies that enforce data protection and encryption requirements for Azure resources.", "policyDefinitionId": "[variables('policySetDefinitionId')]", "scope": "[variables('assignmentScope')]", "enforcementMode": "[parameters('enforcementMode')]", "parameters": {}, "metadata": {"category": "Data Protection", "source": "Azure Landing Zone", "version": "1.0.0"}}}], "outputs": {"policyAssignmentId": {"type": "string", "value": "[resourceId('Microsoft.Authorization/policyAssignments', variables('policyAssignmentName'))]"}, "policyAssignmentName": {"type": "string", "value": "[variables('policyAssignmentName')]"}, "policyAssignmentScope": {"type": "string", "value": "[variables('assignmentScope')]"}}}