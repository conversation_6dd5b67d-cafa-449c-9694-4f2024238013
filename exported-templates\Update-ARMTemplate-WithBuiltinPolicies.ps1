#Requires -Mo<PERSON>les <PERSON>z.Accounts, Az.Resources

<#
.SYNOPSIS
    Update ARM template with built-in policy definition IDs

.DESCRIPTION
    This script updates the ARM template by mapping policy display names to known built-in policy definition IDs.
    For policies that cannot be mapped, it removes them from the template.

.PARAMETER TemplateFile
    Path to the ARM template file to update

.PARAMETER OutputFile
    Path for the updated ARM template file

.PARAMETER KeepManagementGroupsOnly
    If specified, removes all policy assignments and keeps only management groups

.EXAMPLE
    .\Update-ARMTemplate-WithBuiltinPolicies.ps1 -KeepManagementGroupsOnly
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$TemplateFile = ".\mg-hierarchy-arm-template.json",
    
    [Parameter(Mandatory = $false)]
    [string]$OutputFile = ".\mg-hierarchy-clean.json",
    
    [Parameter(Mandatory = $false)]
    [switch]$KeepManagementGroupsOnly
)

Write-Host "Updating ARM Template with Built-in Policies" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

# Load ARM template
if (-not (Test-Path $TemplateFile)) {
    Write-Host "Template file not found: $TemplateFile" -ForegroundColor Red
    exit 1
}

Write-Host "Loading ARM template: $TemplateFile" -ForegroundColor Yellow

try {
    $template = Get-Content $TemplateFile | ConvertFrom-Json
} catch {
    Write-Host "Error loading template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Common built-in policy mappings (these are real Azure built-in policy IDs)
$builtinPolicyMappings = @{
    # Resource Location and Compliance
    "Audit-ResourceRGLocation" = "/providers/Microsoft.Authorization/policyDefinitions/0a914e76-4921-4c19-b460-a2d36003525a"
    
    # Security and Monitoring
    "Deploy-ASC-Monitoring" = "/providers/Microsoft.Authorization/policyDefinitions/17f0cddd-6a5d-4d9b-b7db-8c6b8b0d5b5b"
    "Audit-TrustedLaunch" = "/providers/Microsoft.Authorization/policyDefinitions/c95b54ad-0614-4633-ab29-104b01235cbf"
    "Audit-UnusedResources" = "/providers/Microsoft.Authorization/policyDefinitions/e9c8d085-d9cc-4b17-9cdc-059f1f01f19e"
    
    # Network Security
    "Deny-IP-forwarding" = "/providers/Microsoft.Authorization/policyDefinitions/88c0b9da-ce96-4b03-9635-f29a937e2900"
    "Deny-MgmtPorts-Internet" = "/providers/Microsoft.Authorization/policyDefinitions/22730e10-96f6-4aac-ad84-9383d35b5917"
    "Deny-Subnet-Without-Nsg" = "/providers/Microsoft.Authorization/policyDefinitions/e71308d3-144b-4262-b144-efdc3cc90517"
    
    # Storage Security
    "Deny-Storage-http" = "/providers/Microsoft.Authorization/policyDefinitions/404c3081-a854-4457-ae30-26a93ef643f9"
    
    # Classic Resources
    "Deny-Classic-Resources" = "/providers/Microsoft.Authorization/policyDefinitions/6fac406b-40ca-413b-bf8e-0bf964659c25"
    
    # VM and Compute
    "Deny-UnmanagedDisk" = "/providers/Microsoft.Authorization/policyDefinitions/06a78e20-9358-41c9-923c-fb736d382a4d"
    
    # AKS Security
    "Enforce-AKS-HTTPS" = "/providers/Microsoft.Authorization/policyDefinitions/1a5b4dca-0b6f-4cf5-907c-56316bc1bf3d"
    "Deny-Privileged-AKS" = "/providers/Microsoft.Authorization/policyDefinitions/95edb821-ddaf-4404-9732-666045e056b4"
    "Deny-Priv-Esc-AKS" = "/providers/Microsoft.Authorization/policyDefinitions/1c6e92c9-99f0-4e55-9cf2-0c234dc48f99"
}

Write-Host "Built-in policy mappings available: $($builtinPolicyMappings.Count)" -ForegroundColor Green

# Get resources from template
$managementGroups = $template.resources | Where-Object { $_.type -eq "Microsoft.Management/managementGroups" }
$policyAssignments = $template.resources | Where-Object { $_.type -eq "Microsoft.Authorization/policyAssignments" }

Write-Host "Template analysis:" -ForegroundColor Yellow
Write-Host "  Management Groups: $($managementGroups.Count)" -ForegroundColor White
Write-Host "  Policy Assignments: $($policyAssignments.Count)" -ForegroundColor White

if ($KeepManagementGroupsOnly) {
    Write-Host ""
    Write-Host "Creating Management Groups Only template..." -ForegroundColor Yellow
    
    # Create new template with only management groups
    $newTemplate = @{
        '$schema' = $template.'$schema'
        contentVersion = $template.contentVersion
        metadata = @{
            description = "Management Group hierarchy for demo-ewh (Management Groups only - cleaned)"
            author = "Generated from existing Azure resources (Cleaned)"
            generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        }
        parameters = $template.parameters
        variables = $template.variables
        resources = $managementGroups
        outputs = @{
            managementGroupIds = @{
                type = "array"
                value = @()
            }
        }
    }
    
    # Update outputs to include only management groups
    foreach ($mg in $managementGroups) {
        $newTemplate.outputs.managementGroupIds.value += "[resourceId('Microsoft.Management/managementGroups', '$($mg.name)')]"
    }
    
    Write-Host "✅ Created clean template with $($managementGroups.Count) Management Groups" -ForegroundColor Green
    
} else {
    Write-Host ""
    Write-Host "Updating policy assignments with built-in policy IDs..." -ForegroundColor Yellow
    
    $fixedCount = 0
    $removedCount = 0
    $keptPolicyAssignments = @()
    
    foreach ($assignment in $policyAssignments) {
        $displayName = $assignment.properties.displayName
        
        if ($builtinPolicyMappings.ContainsKey($displayName)) {
            $assignment.properties.policyDefinitionId = $builtinPolicyMappings[$displayName]
            $keptPolicyAssignments += $assignment
            $fixedCount++
            Write-Host "  ✅ Fixed: $displayName" -ForegroundColor Green
        } else {
            $removedCount++
            Write-Host "  ❌ Removed: $displayName (no built-in mapping)" -ForegroundColor Red
        }
    }
    
    # Create new template with fixed policies
    $newTemplate = @{
        '$schema' = $template.'$schema'
        contentVersion = $template.contentVersion
        metadata = @{
            description = "Management Group hierarchy for demo-ewh (with built-in policies only)"
            author = "Generated from existing Azure resources (Built-in policies only)"
            generatedDate = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
        }
        parameters = $template.parameters
        variables = $template.variables
        resources = @()
        outputs = $template.outputs
    }
    
    # Add management groups first
    $newTemplate.resources += $managementGroups
    
    # Add fixed policy assignments
    $newTemplate.resources += $keptPolicyAssignments
    
    Write-Host ""
    Write-Host "Policy Assignment Summary:" -ForegroundColor Yellow
    Write-Host "  Original policy assignments: $($policyAssignments.Count)" -ForegroundColor White
    Write-Host "  Fixed with built-in policies: $fixedCount" -ForegroundColor Green
    Write-Host "  Removed (no mapping): $removedCount" -ForegroundColor Red
    Write-Host "  Final policy assignments: $($keptPolicyAssignments.Count)" -ForegroundColor White
}

# Save updated template
Write-Host ""
Write-Host "Saving updated template to: $OutputFile" -ForegroundColor Yellow

try {
    $newTemplate | ConvertTo-Json -Depth 20 | Set-Content $OutputFile -Encoding UTF8
    Write-Host "✅ Template saved successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error saving template: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create parameters file
$parametersFile = $OutputFile.Replace('.json', '.parameters.json')
$parametersContent = @{
    '$schema' = "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#"
    contentVersion = "*******"
    parameters = @{
        enterpriseScaleCompanyPrefix = @{
            value = "demo-ewh"
        }
    }
}

$parametersContent | ConvertTo-Json -Depth 10 | Set-Content $parametersFile -Encoding UTF8
Write-Host "✅ Parameters file saved: $parametersFile" -ForegroundColor Green

# Create deployment script
$deploymentScript = $OutputFile.Replace('.json', '-Deploy.ps1')
$scriptContent = @"
#Requires -Modules Az.Accounts, Az.Resources

param(
    [Parameter(Mandatory = `$false)]
    [switch]`$WhatIf
)

`$templateFile = "$($OutputFile | Split-Path -Leaf)"
`$parametersFile = "$($parametersFile | Split-Path -Leaf)"
`$deploymentName = "mg-hierarchy-clean-`$(Get-Date -Format 'yyyyMMdd-HHmmss')"
`$location = "East US"

Write-Host "Deploying Clean Management Group Hierarchy" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

if (`$WhatIf) {
    Write-Host "Running What-If deployment..." -ForegroundColor Yellow
    New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile -WhatIf
} else {
    Write-Host "Starting deployment..." -ForegroundColor Yellow
    `$confirmation = Read-Host "Do you want to continue? (y/N)"
    if (`$confirmation -eq 'y' -or `$confirmation -eq 'Y') {
        New-AzTenantDeployment -Name `$deploymentName -Location `$location -TemplateFile `$templateFile -TemplateParameterFile `$parametersFile
    } else {
        Write-Host "Deployment cancelled." -ForegroundColor Yellow
    }
}
"@

$scriptContent | Set-Content $deploymentScript -Encoding UTF8
Write-Host "✅ Deployment script saved: $deploymentScript" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor Yellow
Write-Host "  Template: $OutputFile" -ForegroundColor White
Write-Host "  Parameters: $parametersFile" -ForegroundColor White  
Write-Host "  Deploy Script: $deploymentScript" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Test with What-If: .\$($deploymentScript | Split-Path -Leaf) -WhatIf" -ForegroundColor White
Write-Host "2. Deploy: .\$($deploymentScript | Split-Path -Leaf)" -ForegroundColor White

if ($KeepManagementGroupsOnly) {
    Write-Host ""
    Write-Host "✅ Recommended: This template contains only Management Groups and should deploy successfully!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️  Note: This template contains only policies with known built-in mappings." -ForegroundColor Yellow
    Write-Host "   Custom policies were removed to ensure deployment success." -ForegroundColor Yellow
}
