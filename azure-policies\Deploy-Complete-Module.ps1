# Complete Azure Policy Module Deployment Script
# This script deploys the entire Azure Policy module for Azure Landing Zone

param(
    [Parameter(Mandatory = $true)]
    [ValidateLength(1, 10)]
    [string]$EnterpriseScaleCompanyPrefix,

    [Parameter(Mandatory = $false)]
    [ValidateSet("All", "Definitions", "Initiatives", "Assignments")]
    [string]$DeploymentMode = "All",

    [Parameter(Mandatory = $false)]
    [string]$ManagementGroupId,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

Write-Host "=== Azure Policy Module Deployment ===" -ForegroundColor Cyan
Write-Host "Enterprise Scale Company Prefix: $EnterpriseScaleCompanyPrefix" -ForegroundColor White
Write-Host "Deployment Mode: $DeploymentMode" -ForegroundColor White
Write-Host "What-If Mode: $($WhatIf.IsPresent)" -ForegroundColor White

# Set default management group if not provided
if (-not $ManagementGroupId) {
    $ManagementGroupId = "mg-Platform-Management"
}

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$deploymentResults = @()

# Step 1: Deploy Policy Definitions
if ($DeploymentMode -eq "All" -or $DeploymentMode -eq "Definitions") {
    Write-Host "`n1. Deploying Policy Definitions..." -ForegroundColor Yellow
    try {
        $definitionsPath = Join-Path $scriptPath "policy-definitions"
        Push-Location $definitionsPath
        
        if ($WhatIf) {
            & ".\Deploy-Policies.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -TemplateFile "policies-basic.json" -WhatIf
        } else {
            & ".\Deploy-Policies.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -TemplateFile "policies-basic.json"
        }
        
        $deploymentResults += @{
            Component = "Policy Definitions"
            Status = "Success"
        }
        Write-Host "   Policy Definitions deployed successfully!" -ForegroundColor Green
    }
    catch {
        $deploymentResults += @{
            Component = "Policy Definitions"
            Status = "Failed"
            Error = $_.Exception.Message
        }
        Write-Host "   Failed to deploy Policy Definitions: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

# Step 2: Deploy Policy Initiatives
if ($DeploymentMode -eq "All" -or $DeploymentMode -eq "Initiatives") {
    Write-Host "`n2. Deploying Policy Initiatives..." -ForegroundColor Yellow
    try {
        $initiativesPath = Join-Path $scriptPath "policy-initiatives"
        Push-Location $initiativesPath
        
        if ($WhatIf) {
            & ".\Deploy-Initiatives.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -TemplateFile "initiatives-basic.json" -WhatIf
        } else {
            & ".\Deploy-Initiatives.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -TemplateFile "initiatives-basic.json"
        }
        
        $deploymentResults += @{
            Component = "Policy Initiatives"
            Status = "Success"
        }
        Write-Host "   Policy Initiatives deployed successfully!" -ForegroundColor Green
    }
    catch {
        $deploymentResults += @{
            Component = "Policy Initiatives"
            Status = "Failed"
            Error = $_.Exception.Message
        }
        Write-Host "   Failed to deploy Policy Initiatives: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

# Step 3: Deploy Policy Assignments
if ($DeploymentMode -eq "All" -or $DeploymentMode -eq "Assignments") {
    Write-Host "`n3. Deploying Policy Assignments..." -ForegroundColor Yellow
    try {
        $assignmentsPath = Join-Path $scriptPath "policy-assignments"
        Push-Location $assignmentsPath
        
        # Deploy Security policies
        Write-Host "   Deploying Security policies to $ManagementGroupId..." -ForegroundColor Cyan
        if ($WhatIf) {
            & ".\Deploy-Assignments.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -PolicyType "Security" -ManagementGroupId $ManagementGroupId -WhatIf
        } else {
            & ".\Deploy-Assignments.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -PolicyType "Security" -ManagementGroupId $ManagementGroupId
        }
        
        # Deploy Data Protection policies
        Write-Host "   Deploying Data Protection policies to $ManagementGroupId..." -ForegroundColor Cyan
        if ($WhatIf) {
            & ".\Deploy-Assignments.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -PolicyType "DataProtection" -ManagementGroupId $ManagementGroupId -WhatIf
        } else {
            & ".\Deploy-Assignments.ps1" -EnterpriseScaleCompanyPrefix $EnterpriseScaleCompanyPrefix -PolicyType "DataProtection" -ManagementGroupId $ManagementGroupId
        }
        
        $deploymentResults += @{
            Component = "Policy Assignments"
            Status = "Success"
        }
        Write-Host "   Policy Assignments deployed successfully!" -ForegroundColor Green
    }
    catch {
        $deploymentResults += @{
            Component = "Policy Assignments"
            Status = "Failed"
            Error = $_.Exception.Message
        }
        Write-Host "   Failed to deploy Policy Assignments: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        Pop-Location
    }
}

# Summary
Write-Host "`n=== Deployment Summary ===" -ForegroundColor Cyan
foreach ($result in $deploymentResults) {
    $status = switch ($result.Status) {
        "Success" { "✅" }
        "Failed" { "❌" }
    }
    Write-Host "$status $($result.Component): $($result.Status)" -ForegroundColor White
    if ($result.Error) {
        Write-Host "   Error: $($result.Error)" -ForegroundColor Red
    }
}

Write-Host "`n=== Next Steps ===" -ForegroundColor Cyan
Write-Host "1. Monitor policy compliance in Azure Portal" -ForegroundColor White
Write-Host "2. Review policy assignments: Get-AzPolicyAssignment" -ForegroundColor White
Write-Host "3. Check policy evaluation results" -ForegroundColor White
Write-Host "4. Configure policy remediation tasks if needed" -ForegroundColor White

Write-Host "`n🎉 Azure Policy Module deployment completed!" -ForegroundColor Green
